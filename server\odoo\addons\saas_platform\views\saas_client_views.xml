<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Client List View -->
    <record id="view_saas_client_list" model="ir.ui.view">
        <field name="name">saas.client.list</field>
        <field name="model">saas.client</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="SaaS Clients">
                <field name="name"/>
                <field name="email"/>
                <field name="company_name"/>
                <field name="state"/>
                <field name="created_date"/>
            </list>
        </field>
    </record>

    <!-- SaaS Client Form View -->
    <record id="view_saas_client_form" model="ir.ui.view">
        <field name="name">saas.client.form</field>
        <field name="model">saas.client</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="SaaS Client">
                <header>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-primary" invisible="state == 'active'"/>
                    <button name="action_suspend" string="Suspend" type="object" 
                            class="btn-warning" invisible="state != 'active'"/>
                    <button name="action_cancel" string="Cancel" type="object" 
                            class="btn-danger" invisible="state == 'cancelled'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,suspended,cancelled"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_saas_instance)d" type="action"
                                class="oe_stat_button" icon="fa-server"
                                context="{'default_client_id': active_id}">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>

                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name"/>
                            <field name="email"/>
                            <field name="phone"/>
                        </group>
                        <group name="company_info" string="Company Information">
                            <field name="company_name"/>
                            <field name="created_date" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Instances" name="instances">
                            <field name="instance_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="plan_id"/>
                                    <field name="subdomain"/>
                                    <field name="status"/>
                                    <field name="expiry_date"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- SaaS Client Search View -->
    <record id="view_saas_client_search" model="ir.ui.view">
        <field name="name">saas.client.search</field>
        <field name="model">saas.client</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Clients">
                <field name="name"/>
                <field name="email"/>
                <field name="company_name"/>
                <filter name="active" string="Active" domain="[('state', '=', 'active')]"/>
                <filter name="suspended" string="Suspended" domain="[('state', '=', 'suspended')]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_state" string="Status" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Client Action -->
    <record id="action_saas_client" model="ir.actions.act_window">
        <field name="name">SaaS Clients</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_saas_client_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS client!
            </p>
            <p>
                Manage your SaaS clients and their subscriptions from here.
            </p>
        </field>
    </record>
</odoo>

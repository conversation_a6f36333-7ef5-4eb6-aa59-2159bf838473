# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class SaasDomain(models.Model):
    _name = 'saas.domain'
    _description = 'SaaS Domain'
    _inherit = ['mail.thread']
    _order = 'sequence, name'

    name = fields.Char(
        string='Domain Name',
        required=True,
        help='Base domain name (e.g., myapp.com)'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True
    )
    
    # Server Configuration
    server_id = fields.Many2one(
        'saas.server',
        string='Server',
        required=True,
        help='Server hosting this domain'
    )
    
    # SSL Configuration
    ssl_enabled = fields.Boolean(
        string='SSL Enabled',
        default=True
    )
    
    ssl_certificate = fields.Text(
        string='SSL Certificate'
    )
    
    ssl_private_key = fields.Text(
        string='SSL Private Key'
    )
    
    ssl_expiry_date = fields.Datetime(
        string='SSL Expiry Date'
    )
    
    # Wildcard SSL
    wildcard_ssl = fields.Boolean(
        string='Wildcard SSL',
        default=True,
        help='Whether to use wildcard SSL certificate'
    )
    
    # DNS Configuration
    dns_provider = fields.Selection([
        ('cloudflare', 'Cloudflare'),
        ('route53', 'AWS Route 53'),
        ('godaddy', 'GoDaddy'),
        ('namecheap', 'Namecheap'),
        ('manual', 'Manual'),
    ], string='DNS Provider', default='manual')
    
    dns_api_key = fields.Char(
        string='DNS API Key',
        help='API key for DNS provider'
    )
    
    dns_api_secret = fields.Char(
        string='DNS API Secret',
        help='API secret for DNS provider'
    )
    
    # Statistics
    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        store=True
    )
    
    # Instances using this domain
    instance_ids = fields.One2many(
        'saas.instance',
        'domain_id',
        string='Instances'
    )

    @api.depends('instance_ids')
    def _compute_instance_count(self):
        for domain in self:
            domain.instance_count = len(domain.instance_ids)

    @api.constrains('name')
    def _check_domain_format(self):
        for domain in self:
            if domain.name:
                import re
                # Basic domain validation
                pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
                if not re.match(pattern, domain.name):
                    raise ValidationError(_('Invalid domain name format.'))

    def action_configure_ssl(self):
        """Configure SSL certificate for the domain"""
        self.ensure_one()
        
        try:
            if self.wildcard_ssl:
                self._generate_wildcard_ssl()
            else:
                self._generate_ssl_certificate()
            
            self.message_post(body=_('SSL certificate configured successfully'))
            
        except Exception as e:
            _logger.error(f"SSL configuration failed for domain {self.name}: {str(e)}")
            raise

    def _generate_wildcard_ssl(self):
        """Generate wildcard SSL certificate"""
        _logger.info(f"Generating wildcard SSL for domain {self.name}")
        # Implementation would use Let's Encrypt with DNS challenge
        
        # Simulate SSL generation
        from datetime import timedelta
        self.ssl_expiry_date = fields.Datetime.now() + timedelta(days=90)

    def _generate_ssl_certificate(self):
        """Generate regular SSL certificate"""
        _logger.info(f"Generating SSL certificate for domain {self.name}")
        # Implementation would use Let's Encrypt
        
        # Simulate SSL generation
        from datetime import timedelta
        self.ssl_expiry_date = fields.Datetime.now() + timedelta(days=90)

    def action_view_instances(self):
        """View instances using this domain"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Instances'),
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('domain_id', '=', self.id)],
            'context': {'default_domain_id': self.id},
        }


class SaasApp(models.Model):
    _name = 'saas.app'
    _description = 'SaaS App/Module'
    _order = 'sequence, name'

    name = fields.Char(
        string='App Name',
        required=True
    )
    
    technical_name = fields.Char(
        string='Technical Name',
        required=True,
        help='Technical name of the Odoo module'
    )
    
    description = fields.Text(
        string='Description'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10
    )
    
    active = fields.Boolean(
        string='Active',
        default=True
    )
    
    # App Category
    category = fields.Selection([
        ('accounting', 'Accounting'),
        ('sales', 'Sales'),
        ('purchase', 'Purchase'),
        ('inventory', 'Inventory'),
        ('manufacturing', 'Manufacturing'),
        ('project', 'Project'),
        ('hr', 'Human Resources'),
        ('marketing', 'Marketing'),
        ('website', 'Website'),
        ('ecommerce', 'E-commerce'),
        ('custom', 'Custom'),
    ], string='Category', default='custom')
    
    # Pricing
    monthly_price = fields.Monetary(
        string='Monthly Price',
        currency_field='currency_id',
        help='Additional monthly price for this app'
    )
    
    yearly_price = fields.Monetary(
        string='Yearly Price',
        currency_field='currency_id',
        help='Additional yearly price for this app'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Dependencies
    depends_on_ids = fields.Many2many(
        'saas.app',
        'saas_app_dependency_rel',
        'app_id',
        'dependency_id',
        string='Depends On',
        help='Apps that this app depends on'
    )
    
    # Version Compatibility
    odoo_version_ids = fields.Many2many(
        'saas.odoo.version',
        string='Compatible Versions',
        help='Odoo versions this app is compatible with'
    )
    
    # Installation
    auto_install = fields.Boolean(
        string='Auto Install',
        default=False,
        help='Whether to auto-install this app'
    )
    
    # Statistics
    installation_count = fields.Integer(
        string='Installation Count',
        compute='_compute_installation_count'
    )

    @api.depends()
    def _compute_installation_count(self):
        for app in self:
            count = self.env['saas.instance'].search_count([
                ('installed_app_ids', 'in', app.id)
            ])
            app.installation_count = count

    @api.constrains('technical_name')
    def _check_technical_name_unique(self):
        for app in self:
            if app.technical_name:
                existing = self.search([
                    ('technical_name', '=', app.technical_name),
                    ('id', '!=', app.id)
                ])
                if existing:
                    raise ValidationError(_('Technical name must be unique.'))


class SaasSubscription(models.Model):
    _name = 'saas.subscription'
    _description = 'SaaS Subscription'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Subscription Name',
        required=True,
        tracking=True
    )
    
    # Client and Instance
    client_id = fields.Many2one(
        'saas.client',
        string='Client',
        required=True,
        ondelete='cascade'
    )
    
    instance_id = fields.Many2one(
        'saas.instance',
        string='Instance',
        required=True,
        ondelete='cascade'
    )
    
    plan_id = fields.Many2one(
        'saas.plan',
        string='Plan',
        required=True
    )
    
    # Subscription Details
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
    ], string='Status', default='draft', tracking=True)
    
    billing_period = fields.Selection([
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    ], string='Billing Period', default='monthly', required=True)
    
    # Pricing
    base_amount = fields.Monetary(
        string='Base Amount',
        currency_field='currency_id'
    )
    
    user_count = fields.Integer(
        string='User Count',
        default=1,
        help='Number of users for this subscription'
    )
    
    user_amount = fields.Monetary(
        string='User Amount',
        currency_field='currency_id',
        compute='_compute_amounts',
        store=True
    )
    
    app_amount = fields.Monetary(
        string='App Amount',
        currency_field='currency_id',
        compute='_compute_amounts',
        store=True
    )
    
    amount_total = fields.Monetary(
        string='Total Amount',
        currency_field='currency_id',
        compute='_compute_amounts',
        store=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Additional Apps
    additional_app_ids = fields.Many2many(
        'saas.app',
        'saas_subscription_app_rel',
        'subscription_id',
        'app_id',
        string='Additional Apps'
    )
    
    # Dates
    start_date = fields.Date(
        string='Start Date',
        default=fields.Date.today,
        required=True
    )
    
    end_date = fields.Date(
        string='End Date'
    )
    
    next_billing_date = fields.Date(
        string='Next Billing Date',
        compute='_compute_next_billing_date',
        store=True
    )
    
    # Payment
    payment_method_id = fields.Many2one(
        'payment.method',
        string='Payment Method'
    )
    
    auto_renew = fields.Boolean(
        string='Auto Renew',
        default=True
    )

    @api.depends('plan_id', 'billing_period', 'user_count', 'additional_app_ids')
    def _compute_amounts(self):
        for subscription in self:
            if subscription.plan_id:
                # Base amount
                if subscription.billing_period == 'monthly':
                    subscription.base_amount = subscription.plan_id.monthly_price
                    user_price = subscription.plan_id.price_per_user_monthly
                else:
                    subscription.base_amount = subscription.plan_id.yearly_price
                    user_price = subscription.plan_id.price_per_user_yearly
                
                # User amount (additional users)
                additional_users = max(0, subscription.user_count - 1)
                subscription.user_amount = additional_users * user_price
                
                # App amount
                app_amount = 0
                for app in subscription.additional_app_ids:
                    if subscription.billing_period == 'monthly':
                        app_amount += app.monthly_price
                    else:
                        app_amount += app.yearly_price
                subscription.app_amount = app_amount
                
                # Total amount
                subscription.amount_total = subscription.base_amount + subscription.user_amount + subscription.app_amount

    @api.depends('start_date', 'billing_period')
    def _compute_next_billing_date(self):
        for subscription in self:
            if subscription.start_date:
                if subscription.billing_period == 'monthly':
                    from dateutil.relativedelta import relativedelta
                    subscription.next_billing_date = subscription.start_date + relativedelta(months=1)
                else:
                    from dateutil.relativedelta import relativedelta
                    subscription.next_billing_date = subscription.start_date + relativedelta(years=1)

    def action_activate(self):
        """Activate the subscription"""
        self.write({'state': 'active'})

    def action_suspend(self):
        """Suspend the subscription"""
        self.write({'state': 'suspended'})
        # Also suspend the instance
        if self.instance_id.state == 'running':
            self.instance_id.action_suspend()

    def action_cancel(self):
        """Cancel the subscription"""
        self.write({'state': 'cancelled'})

    def action_renew(self):
        """Renew the subscription"""
        if self.billing_period == 'monthly':
            from dateutil.relativedelta import relativedelta
            self.next_billing_date = self.next_billing_date + relativedelta(months=1)
        else:
            from dateutil.relativedelta import relativedelta
            self.next_billing_date = self.next_billing_date + relativedelta(years=1)

# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class SaasClient(models.Model):
    _name = 'saas.client'
    _description = 'SaaS Client'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Client Name',
        required=True,
        tracking=True,
        help='Name of the SaaS client'
    )
    
    partner_id = fields.Many2one(
        'res.partner',
        string='Partner',
        required=True,
        tracking=True,
        help='Related partner record'
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help='Client email address'
    )
    
    phone = fields.Char(
        string='Phone',
        tracking=True,
        help='Client phone number'
    )
    
    company_name = fields.Char(
        string='Company Name',
        tracking=True,
        help='Client company name'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Instance Management
    instance_ids = fields.One2many(
        'saas.instance',
        'client_id',
        string='Instances',
        help='SaaS instances owned by this client'
    )
    
    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        store=True
    )
    
    active_instance_count = fields.Integer(
        string='Active Instances',
        compute='_compute_instance_count',
        store=True
    )
    
    # Trial Management
    trial_instances_used = fields.Integer(
        string='Trial Instances Used',
        default=0,
        help='Number of trial instances already used'
    )
    
    max_trial_instances = fields.Integer(
        string='Max Trial Instances',
        default=3,
        help='Maximum number of trial instances allowed'
    )
    
    can_create_trial = fields.Boolean(
        string='Can Create Trial',
        compute='_compute_can_create_trial',
        help='Whether client can create more trial instances'
    )
    
    # Subscription Management
    subscription_ids = fields.One2many(
        'saas.subscription',
        'client_id',
        string='Subscriptions'
    )
    
    active_subscription_count = fields.Integer(
        string='Active Subscriptions',
        compute='_compute_subscription_count',
        store=True
    )
    
    # Financial
    total_revenue = fields.Monetary(
        string='Total Revenue',
        compute='_compute_financial_data',
        store=True,
        currency_field='currency_id'
    )
    
    monthly_revenue = fields.Monetary(
        string='Monthly Revenue',
        compute='_compute_financial_data',
        store=True,
        currency_field='currency_id'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    # Dates
    registration_date = fields.Datetime(
        string='Registration Date',
        default=fields.Datetime.now,
        tracking=True
    )
    
    last_login_date = fields.Datetime(
        string='Last Login',
        help='Last time client accessed their portal'
    )
    
    # Portal Access
    portal_access_token = fields.Char(
        string='Portal Access Token',
        help='Token for portal access'
    )
    
    # Notes
    notes = fields.Text(
        string='Internal Notes',
        help='Internal notes about the client'
    )

    @api.depends('instance_ids', 'instance_ids.state')
    def _compute_instance_count(self):
        for client in self:
            client.instance_count = len(client.instance_ids)
            client.active_instance_count = len(
                client.instance_ids.filtered(lambda i: i.state == 'running')
            )

    @api.depends('trial_instances_used', 'max_trial_instances')
    def _compute_can_create_trial(self):
        for client in self:
            client.can_create_trial = client.trial_instances_used < client.max_trial_instances

    @api.depends('subscription_ids', 'subscription_ids.state')
    def _compute_subscription_count(self):
        for client in self:
            client.active_subscription_count = len(
                client.subscription_ids.filtered(lambda s: s.state == 'active')
            )

    @api.depends('subscription_ids', 'subscription_ids.amount_total')
    def _compute_financial_data(self):
        for client in self:
            active_subscriptions = client.subscription_ids.filtered(
                lambda s: s.state == 'active'
            )
            client.total_revenue = sum(active_subscriptions.mapped('amount_total'))
            client.monthly_revenue = sum(
                sub.amount_total for sub in active_subscriptions 
                if sub.billing_period == 'monthly'
            )

    @api.constrains('email')
    def _check_email(self):
        for client in self:
            if client.email:
                # Basic email validation
                if '@' not in client.email or '.' not in client.email:
                    raise ValidationError(_('Please enter a valid email address.'))

    @api.model
    def create(self, vals):
        # Create partner if not provided
        if 'partner_id' not in vals and vals.get('email'):
            partner_vals = {
                'name': vals.get('name', vals.get('email')),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'is_company': bool(vals.get('company_name')),
                'customer_rank': 1,
            }
            if vals.get('company_name'):
                partner_vals['name'] = vals['company_name']
            
            partner = self.env['res.partner'].create(partner_vals)
            vals['partner_id'] = partner.id
        
        # Generate portal access token
        if 'portal_access_token' not in vals:
            vals['portal_access_token'] = self._generate_access_token()
        
        client = super().create(vals)
        
        # Send welcome email
        client._send_welcome_email()
        
        return client

    def _generate_access_token(self):
        """Generate a unique access token for portal access"""
        import secrets
        return secrets.token_urlsafe(32)

    def _send_welcome_email(self):
        """Send welcome email to new client"""
        template = self.env.ref('saas_management.email_template_client_welcome', raise_if_not_found=False)
        if template:
            template.send_mail(self.id, force_send=True)

    def action_activate(self):
        """Activate the client"""
        self.write({'state': 'active'})
        self.message_post(body=_('Client activated'))

    def action_suspend(self):
        """Suspend the client and all their instances"""
        self.write({'state': 'suspended'})
        # Suspend all active instances
        active_instances = self.instance_ids.filtered(lambda i: i.state == 'running')
        active_instances.action_suspend()
        self.message_post(body=_('Client suspended'))

    def action_cancel(self):
        """Cancel the client"""
        self.write({'state': 'cancelled'})
        # Stop all instances
        running_instances = self.instance_ids.filtered(lambda i: i.state in ['running', 'suspended'])
        running_instances.action_stop()
        self.message_post(body=_('Client cancelled'))

    def create_trial_instance(self, plan_id, subdomain):
        """Create a trial instance for the client"""
        if not self.can_create_trial:
            raise UserError(_('Maximum trial instances reached'))
        
        instance_vals = {
            'name': f'{self.name} - Trial',
            'client_id': self.id,
            'plan_id': plan_id,
            'subdomain': subdomain,
            'instance_type': 'trial',
            'state': 'draft',
        }
        
        instance = self.env['saas.instance'].create(instance_vals)
        self.trial_instances_used += 1
        
        return instance

    def get_portal_url(self):
        """Get the portal URL for this client"""
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        return f"{base_url}/my/saas?access_token={self.portal_access_token}"

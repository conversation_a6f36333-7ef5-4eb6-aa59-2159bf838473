2025-06-27 05:07:24,511 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:24] "POST /web/action/load HTTP/1.1" 200 - 12 0.010 0.008
2025-06-27 05:07:24,935 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:24] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 66 0.035 0.058
2025-06-27 05:07:25,111 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:25] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 3 0.001 0.003
2025-06-27 05:07:25,325 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:25] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 8 0.009 0.055
2025-06-27 05:07:25,332 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:25] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 85 0.050 0.022
2025-06-27 05:07:26,777 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:26] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 85 0.033 0.018
2025-06-27 05:07:27,137 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:27] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 10 0.009 0.038
2025-06-27 05:07:28,848 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:28] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 85 0.037 0.026
2025-06-27 05:07:29,175 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:29] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 8 0.006 0.008
2025-06-27 05:07:32,699 31508 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_install on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 05:07:32,699 31508 INFO ecomplus odoo.addons.base.models.ir_module: User #2 triggered module installation 
2025-06-27 05:07:32,699 31508 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 05:07:32,760 31508 WARNING ecomplus odoo.modules.module: python external dependency on 'hashlib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 05:07:32,760 31508 WARNING ecomplus odoo.modules.module: python external dependency on 'urllib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 05:07:32,815 31508 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:07:32,821 31508 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:07:32,839 31508 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-27 05:07:32,840 31508 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-27 05:07:33,733 31508 INFO ecomplus odoo.modules.loading: loading 175 modules... 
2025-06-27 05:07:33,816 31508 INFO ecomplus odoo.modules.loading: 175 modules loaded in 0.08s, 0 queries (+0 extra) 
2025-06-27 05:07:33,818 31508 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:07:33,818 31508 INFO ecomplus odoo.modules.loading: Loading module multichannel_ecommerce (128/176) 
2025-06-27 05:07:34,042 31508 INFO ecomplus odoo.modules.registry: module multichannel_ecommerce: creating or updating database tables 
2025-06-27 05:07:34,747 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/multichannel_security.xml 
2025-06-27 05:07:34,909 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/ir.model.access.csv 
2025-06-27 05:07:34,932 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/platform_data.xml 
2025-06-27 05:07:34,947 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/sequence_data.xml 
2025-06-27 05:07:34,956 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/multichannel_shop_views.xml 
2025-06-27 05:07:35,008 31508 WARNING ecomplus odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-06-27 05:07:35,010 31508 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-cube) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 29,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3849,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 05:07:35,010 31508 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-shopping-cart) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 32,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3849,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 05:07:35,010 31508 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-money) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 37,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3849,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 05:07:35,028 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/product_mapping_views.xml 
2025-06-27 05:07:35,046 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/transaction_views.xml 
2025-06-27 05:07:35,063 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/advertising_views.xml 
2025-06-27 05:07:35,083 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/affiliate_views.xml 
2025-06-27 05:07:35,107 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/warehouse_mapping_views.xml 
2025-06-27 05:07:35,128 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/menu_views.xml 
2025-06-27 05:07:35,187 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/oauth_templates.xml 
2025-06-27 05:07:35,212 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/dashboard_templates.xml 
2025-06-27 05:07:35,222 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/shop_sync_wizard_views.xml 
2025-06-27 05:07:35,234 31508 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/product_sync_wizard_views.xml 
2025-06-27 05:07:35,254 31508 INFO ecomplus odoo.addons.base.models.ir_module: module multichannel_ecommerce: no translation for language vi_VN 
2025-06-27 05:07:35,308 31508 WARNING ecomplus odoo.modules.loading: The models ['multichannel.shop.sync.wizard', 'multichannel.product.sync.wizard'] have no access rules in module multichannel_ecommerce, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
multichannel_ecommerce.access_multichannel_shop_sync_wizard,access_multichannel_shop_sync_wizard,multichannel_ecommerce.model_multichannel_shop_sync_wizard,base.group_user,1,0,0,0
multichannel_ecommerce.access_multichannel_product_sync_wizard,access_multichannel_product_sync_wizard,multichannel_ecommerce.model_multichannel_product_sync_wizard,base.group_user,1,0,0,0 
2025-06-27 05:07:35,321 31508 INFO ecomplus odoo.modules.loading: Module multichannel_ecommerce loaded in 1.50s, 778 queries (+778 other) 
2025-06-27 05:07:35,322 31508 INFO ecomplus odoo.modules.loading: 176 modules loaded in 1.50s, 778 queries (+778 extra) 
2025-06-27 05:07:37,376 31508 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:07:37,382 31508 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-27 05:07:37,383 31508 INFO ecomplus odoo.modules.registry: Registry loaded in 4.577s 
2025-06-27 05:07:37,384 31508 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-27 05:07:37,390 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:37] "POST /web/dataset/call_button/ir.module.module/button_immediate_install HTTP/1.1" 200 - 4417 2.672 2.024
2025-06-27 05:07:37,709 31508 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 05:07:39,145 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="title or 'Odoo'" in template 180. Replace by @t-out 
2025-06-27 05:07:39,146 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='request.csrf_token(None)' in template 180. Replace by @t-out 
2025-06-27 05:07:39,146 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='debug' in template 180. Replace by @t-out 
2025-06-27 05:07:39,154 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:39] "GET /odoo HTTP/1.1" 200 - 147 0.073 1.375
2025-06-27 05:07:39,617 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="title or 'Odoo'" in template 181. Replace by @t-out 
2025-06-27 05:07:39,621 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='request.csrf_token(None)' in template 181. Replace by @t-out 
2025-06-27 05:07:39,622 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='debug' in template 181. Replace by @t-out 
2025-06-27 05:07:39,631 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="menu['name']" in template 181. Replace by @t-out 
2025-06-27 05:07:39,649 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website.google_analytics_key' in template 181. Replace by @t-out 
2025-06-27 05:07:39,726 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:39] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 12 0.123 0.013
2025-06-27 05:07:39,941 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:39] "POST /web/action/load HTTP/1.1" 200 - 9 0.003 0.009
2025-06-27 05:07:40,065 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.010 0.012
2025-06-27 05:07:40,130 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 21 0.040 0.032
2025-06-27 05:07:40,138 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "POST /mail/data HTTP/1.1" 200 - 49 0.078 0.037
2025-06-27 05:07:40,184 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "POST /mail/data HTTP/1.1" 200 - 69 0.083 0.056
2025-06-27 05:07:40,273 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 12 0.007 0.010
2025-06-27 05:07:40,390 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.006 0.005
2025-06-27 05:07:40,602 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 10 0.009 0.011
2025-06-27 05:07:40,885 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website_sale_cart_quantity' in template 3496. Replace by @t-out 
2025-06-27 05:07:40,927 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:40] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.005
2025-06-27 05:07:40,959 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:07:40,960 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:07:40,961 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:07:40,962 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:07:41,002 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 519. Replace by @t-out 
2025-06-27 05:07:41,075 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website_sale_cart_quantity' in template 3496. Replace by @t-out 
2025-06-27 05:07:41,210 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:07:41,211 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:07:41,213 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:07:41,214 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:07:41,230 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 519. Replace by @t-out 
2025-06-27 05:07:41,376 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 2213. Replace by @t-out 
2025-06-27 05:07:41,377 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 2213. Replace by @t-out 
2025-06-27 05:07:41,382 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:41] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 590 0.285 1.888
2025-06-27 05:07:42,229 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:07:42] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.004
2025-06-27 05:08:36,938 31508 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-27 05:08:37,012 31508 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 38 checked, 0 removed 
2025-06-27 05:08:37,018 31508 INFO ecomplus odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [913] 
2025-06-27 05:08:37,078 31508 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-27 05:08:37,092 31508 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-27 05:08:37,098 31508 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 2 entries 
2025-06-27 05:08:37,100 31508 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 05:08:37,128 31508 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-27 05:08:37,142 31508 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1537, 1538, 1539, 1540, 1541, 1542, 1543, 1544, 1545, 1546, 1547, 1548, 1549, 1550, 1551, 1552, 1553, 1554, 1555] 
2025-06-27 05:08:37,291 31508 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-27 05:08:37,668 31508 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.729s 
2025-06-27 05:08:37,704 31508 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-27 05:08:37,707 31508 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-27 05:09:28,754 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="title or 'Odoo'" in template 180. Replace by @t-out 
2025-06-27 05:09:28,754 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='request.csrf_token(None)' in template 180. Replace by @t-out 
2025-06-27 05:09:28,754 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='debug' in template 180. Replace by @t-out 
2025-06-27 05:09:28,760 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:28] "GET /odoo/discuss HTTP/1.1" 200 - 90 0.037 1.140
2025-06-27 05:09:28,882 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:28] "GET /web/assets/1/cfcb44f/web.assets_web.min.css HTTP/1.1" 200 - 5 0.009 0.090
2025-06-27 05:09:29,033 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="title or 'Odoo'" in template 181. Replace by @t-out 
2025-06-27 05:09:29,035 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='request.csrf_token(None)' in template 181. Replace by @t-out 
2025-06-27 05:09:29,035 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='debug' in template 181. Replace by @t-out 
2025-06-27 05:09:29,042 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="menu['name']" in template 181. Replace by @t-out 
2025-06-27 05:09:29,062 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website.google_analytics_key' in template 181. Replace by @t-out 
2025-06-27 05:09:29,195 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:29] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.002 0.042
2025-06-27 05:09:29,230 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:29] "GET /web/assets/1/a22814d/web.assets_web_print.min.css HTTP/1.1" 200 - 5 0.008 0.022
2025-06-27 05:09:29,485 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:29] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 321 0.177 0.158
2025-06-27 05:09:29,790 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:29] "GET /web/assets/1/557977c/web.assets_web.min.js HTTP/1.1" 200 - 5 0.003 0.661
2025-06-27 05:09:30,049 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website_sale_cart_quantity' in template 3496. Replace by @t-out 
2025-06-27 05:09:30,133 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,134 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,139 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,140 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,199 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 519. Replace by @t-out 
2025-06-27 05:09:30,305 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website_sale_cart_quantity' in template 3496. Replace by @t-out 
2025-06-27 05:09:30,456 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.001 0.061
2025-06-27 05:09:30,495 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,495 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,498 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,498 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 518. Replace by @t-out 
2025-06-27 05:09:30,515 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 519. Replace by @t-out 
2025-06-27 05:09:30,539 31508 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.015
2025-06-27 05:09:30,548 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 11 0.015 0.008
2025-06-27 05:09:30,661 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.name.split('/').pop()" in template 2213. Replace by @t-out 
2025-06-27 05:09:30,662 31508 WARNING ecomplus odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg.url_code.split('_').pop(0).upper()" in template 2213. Replace by @t-out 
2025-06-27 05:09:30,668 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 224 0.166 1.721
2025-06-27 05:09:30,811 31508 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.011
2025-06-27 05:09:30,920 31508 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.013
2025-06-27 05:09:30,949 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "POST /web/action/load HTTP/1.1" 200 - 9 0.015 0.012
2025-06-27 05:09:30,951 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:30] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.012 0.016
2025-06-27 05:09:31,006 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "POST /mail/data HTTP/1.1" 200 - 47 0.042 0.042
2025-06-27 05:09:31,028 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 21 0.019 0.026
2025-06-27 05:09:31,230 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "POST /mail/data HTTP/1.1" 200 - 68 0.069 0.036
2025-06-27 05:09:31,248 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 12 0.007 0.008
2025-06-27 05:09:31,334 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.003 0.004
2025-06-27 05:09:31,635 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 10 0.008 0.006
2025-06-27 05:09:31,965 31508 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:09:31] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.006
2025-06-27 05:10:26,930 8748 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:10:26,930 8748 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:10:26,930 8748 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:10:26,931 8748 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:10:27,114 8748 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:10:27,127 8748 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:10:27,445 8748 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:10:27,445 8748 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:10:27,497 8748 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:10:27,503 8748 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:10:27,627 8748 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:10:29,886 8748 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:10:29,889 8748 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:10:30,380 8748 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.75s, 0 queries (+0 extra) 
2025-06-27 05:10:30,610 8748 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:10:30,617 8748 INFO ecomplus odoo.modules.registry: Registry loaded in 3.172s 
2025-06-27 05:10:35,380 8748 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1956: DeprecationWarning: Deprecated model.clear_cache(), use registry.clear_cache() instead
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 119, in shell
    self.console(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 83, in console
    return getattr(self, shell)(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 103, in python
    Console(locals=local_vars).interact()
  File "C:\Python312\Lib\code.py", line 241, in interact
    more = self.push(line)
  File "C:\Python312\Lib\code.py", line 267, in push
    more = self.runsource(source, self.filename)
  File "C:\Python312\Lib\code.py", line 74, in runsource
    self.runcode(code)
  File "C:\Python312\Lib\code.py", line 90, in runcode
    exec(code, self.locals)
  File "<console>", line 1, in <module>
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 1956, in clear_caches
    warnings.warn("Deprecated model.clear_cache(), use registry.clear_cache() instead", DeprecationWarning)
 
2025-06-27 05:10:35,381 8748 INFO ecomplus odoo.modules.registry: Invalidating all model caches from clear_caches C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1957 
2025-06-27 05:10:51,938 18684 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 05:10:51,944 18684 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-27 05:10:52,081 18684 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 05:10:54,094 18684 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:10:54,098 18684 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:10:54,562 18684 INFO ? odoo.modules.loading: 176 modules loaded in 2.48s, 0 queries (+0 extra) 
2025-06-27 05:10:54,799 18684 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 05:10:54,807 18684 INFO ? odoo.modules.registry: Registry loaded in 2.916s 
2025-06-27 05:10:54,810 18684 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 05:10:54,881 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:10:54] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 23 0.035 2.957
2025-06-27 05:10:54,882 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:10:54] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 6 0.018 2.772
2025-06-27 05:10:55,231 18684 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 05:10:55,278 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:10:55] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.003 0.072
2025-06-27 05:10:59,469 32852 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:10:59,469 32852 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:10:59,469 32852 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:10:59,469 32852 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:10:59,693 32852 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:10:59,705 32852 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:11:00,009 32852 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 05:11:01,277 32852 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 05:11:01,438 32852 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:11:01,444 32852 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:11:01,555 32852 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:11:03,728 32852 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:03,731 32852 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:04,143 32852 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.59s, 0 queries (+0 extra) 
2025-06-27 05:11:04,368 32852 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:11:04,375 32852 INFO ecomplus odoo.modules.registry: Registry loaded in 3.095s 
2025-06-27 05:11:27,572 36636 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:11:27,573 36636 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:11:27,573 36636 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:11:27,573 36636 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:11:27,764 36636 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:11:27,777 36636 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:11:28,078 36636 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:11:28,078 36636 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:11:28,126 36636 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:11:28,133 36636 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:11:28,255 36636 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:11:30,375 36636 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:30,378 36636 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:30,786 36636 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.53s, 0 queries (+0 extra) 
2025-06-27 05:11:31,006 36636 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:11:31,011 36636 INFO ecomplus odoo.modules.registry: Registry loaded in 2.932s 
2025-06-27 05:11:37,265 36636 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1956: DeprecationWarning: Deprecated model.clear_cache(), use registry.clear_cache() instead
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 119, in shell
    self.console(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 83, in console
    return getattr(self, shell)(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 103, in python
    Console(locals=local_vars).interact()
  File "C:\Python312\Lib\code.py", line 241, in interact
    more = self.push(line)
  File "C:\Python312\Lib\code.py", line 267, in push
    more = self.runsource(source, self.filename)
  File "C:\Python312\Lib\code.py", line 74, in runsource
    self.runcode(code)
  File "C:\Python312\Lib\code.py", line 90, in runcode
    exec(code, self.locals)
  File "<console>", line 23, in <module>
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 1956, in clear_caches
    warnings.warn("Deprecated model.clear_cache(), use registry.clear_cache() instead", DeprecationWarning)
 
2025-06-27 05:11:37,265 36636 INFO ecomplus odoo.modules.registry: Invalidating all model caches from clear_caches C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1957 
2025-06-27 05:11:53,754 18360 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:11:53,755 18360 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:11:53,755 18360 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:11:53,755 18360 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:11:53,938 18360 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:11:53,950 18360 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:11:54,249 18360 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:11:54,249 18360 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:11:54,406 18360 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:11:54,413 18360 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:11:54,533 18360 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:11:56,596 18360 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:56,598 18360 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:11:57,068 18360 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.53s, 0 queries (+0 extra) 
2025-06-27 05:11:57,298 18360 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:11:57,304 18360 INFO ecomplus odoo.modules.registry: Registry loaded in 3.055s 
2025-06-27 05:12:28,977 25352 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:12:28,977 25352 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:12:28,977 25352 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:12:28,977 25352 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:12:29,200 25352 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:12:29,214 25352 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:12:29,531 25352 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 05:12:30,792 25352 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 05:12:30,841 25352 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:12:30,847 25352 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-27 05:12:30,968 25352 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:12:33,101 25352 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:12:33,104 25352 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:12:33,546 25352 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.58s, 0 queries (+0 extra) 
2025-06-27 05:12:33,783 25352 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:12:33,789 25352 INFO ecomplus odoo.modules.registry: Registry loaded in 2.994s 
2025-06-27 05:12:57,903 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:12:57] "GET / HTTP/1.1" 200 - 222 0.222 2.329
2025-06-27 05:12:58,472 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:12:58] "POST /im_livechat/init HTTP/1.1" 200 - 22 0.028 0.018
2025-06-27 05:12:58,609 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:12:58] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 13 0.008 0.176
2025-06-27 05:17:31,563 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:31] "GET /odoo/discuss HTTP/1.1" 200 - 110 0.080 3.530
2025-06-27 05:17:33,648 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:33] "GET /web/assets/1/cfcb44f/web.assets_web.min.css HTTP/1.1" 200 - 5 0.007 2.055
2025-06-27 05:17:33,688 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:33] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.003 1.736
2025-06-27 05:17:33,973 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:33] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 50 0.122 2.266
2025-06-27 05:17:34,035 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 322 0.327 1.761
2025-06-27 05:17:34,086 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/assets/1/a22814d/web.assets_web_print.min.css HTTP/1.1" 200 - 5 0.001 0.005
2025-06-27 05:17:34,163 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/assets/1/557977c/web.assets_web.min.js HTTP/1.1" 200 - 5 0.009 2.253
2025-06-27 05:17:34,796 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.001 0.039
2025-06-27 05:17:34,876 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.004 0.007
2025-06-27 05:17:34,919 18684 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:34] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.055
2025-06-27 05:17:35,021 18684 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 05:17:35,143 18684 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 05:17:35,276 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.015 0.011
2025-06-27 05:17:35,283 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "POST /web/action/load HTTP/1.1" 200 - 9 0.019 0.015
2025-06-27 05:17:35,289 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.019 0.019
2025-06-27 05:17:35,343 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.012 0.007
2025-06-27 05:17:35,376 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "POST /mail/data HTTP/1.1" 200 - 44 0.098 0.029
2025-06-27 05:17:35,541 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "POST /mail/data HTTP/1.1" 200 - 53 0.059 0.036
2025-06-27 05:17:35,660 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "POST /mail/inbox/messages HTTP/1.1" 200 - 7 0.006 0.005
2025-06-27 05:17:35,941 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:35] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.005 0.009
2025-06-27 05:17:36,272 18684 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:36] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.000 0.006
2025-06-27 05:17:42,337 18684 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:17:42,338 18684 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:17:42,391 18684 ERROR ecomplus odoo.sql_db: bad query: b' UPDATE "bus_presence"\n                    SET "status" = "__tmp"."status"::VARCHAR\n                    FROM (VALUES (2, \'offline\')) AS "__tmp"("id", "status")\n                    WHERE "bus_presence"."id" = "__tmp"."id"\n                '
ERROR: could not serialize access due to concurrent update
 
2025-06-27 05:17:42,421 18684 ERROR ecomplus odoo.addons.bus.websocket: could not serialize access due to concurrent update
 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 318, in get_messages
    message = self._process_next_message()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 464, in _process_next_message
    self._handle_control_frame(frame)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 602, in _handle_control_frame
    self._terminate()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 583, in _terminate
    with acquire_cursor(self._db) as cr:
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 201, in __exit__
    self.commit()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 479, in commit
    self.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 159, in flush
    self.transaction.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 1022, in flush
    env_to_flush.flush_all()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 849, in flush_all
    self[model_name].flush_model()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6752, in flush_model
    self._flush(fnames)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6830, in _flush
    model.browse(some_ids)._write_multi(vals_list)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 4916, in _write_multi
    self.env.execute_query(SQL(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 978, in execute_query
    self.cr.execute(query)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 354, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.SerializationFailure: could not serialize access due to concurrent update

2025-06-27 05:17:42,472 18684 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 7 connections  
2025-06-27 05:17:44,420 25352 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 05:17:44,571 25352 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:44] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 9 0.010 0.147
2025-06-27 05:17:44,591 25352 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:44] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 3 0.002 0.005
2025-06-27 05:17:44,930 25352 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 05:17:45,116 25352 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:17:45] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.005 0.199
2025-06-27 05:17:45,272 29716 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:17:45,272 29716 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:17:45,272 29716 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:17:45,273 29716 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:17:45,514 29716 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:17:45,535 29716 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:17:47,264 29716 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 05:20:34,055 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-27 05:20:34,065 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.009s 
2025-06-27 05:20:34,068 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-27 05:20:34,072 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-27 05:20:34,080 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-27 05:20:34,086 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.005s 
2025-06-27 05:20:34,089 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-27 05:20:34,092 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-27 05:20:34,100 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-27 05:20:34,106 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.006s 
2025-06-27 05:20:34,109 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-27 05:20:34,113 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-27 05:20:34,121 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-27 05:20:34,130 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.009s 
2025-06-27 05:20:34,133 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-27 05:20:34,136 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-27 05:20:34,144 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-27 05:20:34,148 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.004s 
2025-06-27 05:20:34,150 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-27 05:20:34,154 25352 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-27 05:27:05,038 31672 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:27:05,038 31672 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:27:05,038 31672 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:27:05,038 31672 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:27:05,233 31672 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:27:05,246 31672 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:27:05,599 31672 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:27:05,599 31672 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:27:05,651 31672 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:27:05,656 31672 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:27:05,868 31672 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:27:08,716 31672 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:08,720 31672 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:09,290 31672 INFO ecomplus odoo.modules.loading: 176 modules loaded in 3.42s, 0 queries (+0 extra) 
2025-06-27 05:27:09,515 31672 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:27:09,521 31672 INFO ecomplus odoo.modules.registry: Registry loaded in 3.921s 
2025-06-27 05:27:32,757 29716 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 05:27:32,763 29716 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:27:32,891 29716 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 05:27:35,275 29716 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:35,278 29716 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:35,753 29716 INFO ? odoo.modules.loading: 176 modules loaded in 2.86s, 0 queries (+0 extra) 
2025-06-27 05:27:35,995 29716 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 05:27:36,003 29716 INFO ? odoo.modules.registry: Registry loaded in 3.293s 
2025-06-27 05:27:36,006 29716 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 05:27:36,084 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:27:36] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 23 0.031 3.343
2025-06-27 05:27:36,085 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:27:36] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 6 0.017 3.145
2025-06-27 05:27:36,446 29716 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 05:27:36,506 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:27:36] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 3 0.004 0.072
2025-06-27 05:27:40,063 16824 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:27:40,063 16824 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:27:40,064 16824 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:27:40,064 16824 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:27:40,294 16824 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:27:40,310 16824 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:27:40,614 16824 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 05:27:41,883 16824 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 05:27:41,931 16824 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:27:41,938 16824 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:27:42,062 16824 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:27:44,266 16824 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:44,270 16824 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:27:44,706 16824 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.64s, 0 queries (+0 extra) 
2025-06-27 05:27:44,928 16824 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:27:44,934 16824 INFO ecomplus odoo.modules.registry: Registry loaded in 3.049s 
2025-06-27 05:28:10,119 39924 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:28:10,120 39924 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:28:10,120 39924 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:28:10,120 39924 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:28:10,328 39924 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:28:10,344 39924 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:28:10,674 39924 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:28:10,674 39924 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:28:10,723 39924 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:28:10,729 39924 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:28:10,855 39924 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:28:13,055 39924 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:28:13,058 39924 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:28:13,490 39924 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.63s, 0 queries (+0 extra) 
2025-06-27 05:28:13,725 39924 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:28:13,730 39924 INFO ecomplus odoo.modules.registry: Registry loaded in 3.055s 
2025-06-27 05:28:38,687 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:28:38] "GET / HTTP/1.1" 200 - 217 0.225 2.439
2025-06-27 05:28:49,317 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:28:49] "GET /odoo HTTP/1.1" 200 - 78 0.052 4.088
2025-06-27 05:28:50,406 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:28:50] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 1 0.002 0.704
2025-06-27 05:28:50,623 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:28:50] "GET /web/webclient/load_menus/4f38ae4690cba90eaf4a7c519fd636e7c2d3989227023750ec6b67b72cc72323 HTTP/1.1" 200 - 211 0.163 0.763
2025-06-27 05:29:00,736 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/2fe85b7/web.assets_web.min.css (id:1559) 
2025-06-27 05:29:00,737 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1538] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/2fe85b7/web.assets_web.min.css 
2025-06-27 05:29:00,912 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:00] "GET /web/assets/2fe85b7/web.assets_web.min.css HTTP/1.1" 200 - 33 0.207 11.326
2025-06-27 05:29:09,667 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/f58a84d/web.assets_web_print.min.css (id:1560) 
2025-06-27 05:29:09,668 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1539] (matching /web/assets/_______/web.assets_web_print.min.css) because it was replaced with /web/assets/f58a84d/web.assets_web_print.min.css 
2025-06-27 05:29:09,738 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:09] "GET /web/assets/f58a84d/web.assets_web_print.min.css HTTP/1.1" 200 - 27 0.186 8.318
2025-06-27 05:29:17,813 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/029520b/web.assets_web.min.js (id:1561) 
2025-06-27 05:29:17,813 29716 INFO ecomplus odoo.addons.base.models.assetsbundle: Deleting attachments [1540] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/029520b/web.assets_web.min.js 
2025-06-27 05:29:18,476 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:18] "GET /web/assets/029520b/web.assets_web.min.js HTTP/1.1" 200 - 30 0.072 28.706
2025-06-27 05:29:19,222 29716 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (U4WZSmSVUMrxcKpHaAv_tkIpkUD3h5xz9B4r8Cl9MW) 
2025-06-27 05:29:19,303 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "POST /mail/data HTTP/1.1" 200 - 44 0.047 0.038
2025-06-27 05:29:19,381 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.011 0.019
2025-06-27 05:29:19,389 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "POST /web/action/load HTTP/1.1" 200 - 6 0.016 0.023
2025-06-27 05:29:19,395 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.019 0.025
2025-06-27 05:29:19,402 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.029 0.020
2025-06-27 05:29:19,569 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "POST /mail/data HTTP/1.1" 200 - 53 0.061 0.034
2025-06-27 05:29:19,764 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.003 0.005
2025-06-27 05:29:19,968 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:19] "GET /web/image/res.partner/2/avatar_128?unique=1750997783000 HTTP/1.1" 200 - 7 0.006 0.011
2025-06-27 05:29:21,213 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:21] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.005
2025-06-27 05:29:31,153 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:31] "POST /web/action/load HTTP/1.1" 200 - 12 0.047 0.120
2025-06-27 05:29:31,505 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:31] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 140 0.086 0.168
2025-06-27 05:29:31,601 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:31] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 20 0.045 0.034
2025-06-27 05:29:31,841 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:31] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 05:29:32,520 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:32] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.007 0.012
2025-06-27 05:29:38,385 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:38] "POST /web/action/load HTTP/1.1" 200 - 10 0.006 0.005
2025-06-27 05:29:38,767 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:38] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 50 0.025 0.033
2025-06-27 05:29:38,978 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:38] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 05:29:39,148 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:39] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 59 0.029 0.022
2025-06-27 05:29:39,218 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:39] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.006 0.113
2025-06-27 05:29:39,591 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:39] "GET /cr_pos_default_payment_method/static/description/icon.png HTTP/1.1" 404 - 40 0.037 0.192
2025-06-27 05:29:42,021 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:42] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 58 0.027 0.017
2025-06-27 05:29:42,278 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:42] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.005 0.036
2025-06-27 05:29:42,415 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:42] "GET /cr_pos_default_payment_method/static/description/icon.png HTTP/1.1" 404 - 28 0.016 0.043
2025-06-27 05:29:45,245 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-27 05:29:45,254 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.008s 
2025-06-27 05:29:45,257 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-27 05:29:45,346 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-27 05:29:45,354 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-27 05:29:45,374 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.021s 
2025-06-27 05:29:45,377 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-27 05:29:45,380 16824 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-27 05:29:45,444 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:45] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 58 0.029 0.018
2025-06-27 05:29:45,667 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:45] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.004
2025-06-27 05:29:54,906 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:54] "GET /odoo/discuss HTTP/1.1" 200 - 92 0.052 1.148
2025-06-27 05:29:55,081 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 38 0.082 0.069
2025-06-27 05:29:55,400 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.005 0.045
2025-06-27 05:29:55,701 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "POST /web/action/load HTTP/1.1" 200 - 9 0.002 0.008
2025-06-27 05:29:55,823 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 9 0.013 0.010
2025-06-27 05:29:55,850 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 6 0.007 0.015
2025-06-27 05:29:55,867 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.021 0.019
2025-06-27 05:29:55,886 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "POST /mail/data HTTP/1.1" 200 - 26 0.059 0.027
2025-06-27 05:29:55,920 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:55] "POST /mail/data HTTP/1.1" 200 - 40 0.082 0.036
2025-06-27 05:29:56,066 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:56] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.003 0.005
2025-06-27 05:29:56,318 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:56] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 9 0.006 0.008
2025-06-27 05:29:56,645 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:56] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.005
2025-06-27 05:29:57,893 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:57] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.001 0.007
2025-06-27 05:29:57,924 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:57] "GET /odoo/discuss HTTP/1.1" 200 - 26 0.012 0.027
2025-06-27 05:29:58,156 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/assets/1/cfcb44f/web.assets_web.min.css HTTP/1.1" 200 - 5 0.001 0.005
2025-06-27 05:29:58,309 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.001 0.052
2025-06-27 05:29:58,314 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/assets/1/557977c/web.assets_web.min.js HTTP/1.1" 200 - 5 0.048 0.011
2025-06-27 05:29:58,350 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/assets/1/a22814d/web.assets_web_print.min.css HTTP/1.1" 200 - 5 0.009 0.025
2025-06-27 05:29:58,425 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 38 0.051 0.062
2025-06-27 05:29:58,667 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 321 0.250 0.161
2025-06-27 05:29:58,837 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:58] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 2 0.001 0.042
2025-06-27 05:29:59,115 29716 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 05:29:59,116 29716 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 05:29:59,176 29716 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 05:29:59,297 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "POST /web/action/load HTTP/1.1" 200 - 9 0.007 0.007
2025-06-27 05:29:59,314 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 8 0.005 0.010
2025-06-27 05:29:59,328 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "POST /mail/data HTTP/1.1" 200 - 26 0.025 0.019
2025-06-27 05:29:59,443 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.009 0.007
2025-06-27 05:29:59,491 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "POST /mail/data HTTP/1.1" 200 - 40 0.034 0.032
2025-06-27 05:29:59,508 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.006 0.014
2025-06-27 05:29:59,620 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.003 0.004
2025-06-27 05:29:59,683 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 6 0.003 0.006
2025-06-27 05:29:59,878 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:29:59] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 5 0.003 0.002
2025-06-27 05:30:00,201 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:00] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.004
2025-06-27 05:30:09,567 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:09] "GET /web/session/logout HTTP/1.1" 303 - 4 0.001 0.006
2025-06-27 05:30:09,801 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:09] "GET /odoo HTTP/1.1" 303 - 4 0.004 0.004
2025-06-27 05:30:10,272 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:10] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 124 0.095 0.353
2025-06-27 05:30:10,486 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:10] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 32 0.026 0.167
2025-06-27 05:30:10,691 29716 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:10] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 0 0.000 0.003
2025-06-27 05:30:11,072 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:11] "GET /website/translations/8d0668eb991f20961a3d2eb1a7d3709f8e39eb75?lang=vi_VN HTTP/1.1" 200 - 4 0.003 0.027
2025-06-27 05:30:11,418 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:11] "POST /im_livechat/init HTTP/1.1" 200 - 13 0.010 0.010
2025-06-27 05:30:11,433 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:11] "GET /im_livechat/assets_embed.css HTTP/1.1" 200 - 16 0.007 0.138
2025-06-27 05:30:14,516 29716 INFO ecomplus odoo.addons.base.models.res_users: Login successful for db:ecomplus login:admin from 127.0.0.1 
2025-06-27 05:30:14,530 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:14] "POST /web/login HTTP/1.1" 303 - 28 0.025 0.669
2025-06-27 05:30:14,547 29716 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xs86__dd9U7sa2s_N1d0amQC9a2Hhdxu0LyiSU3Iih) 
2025-06-27 05:30:14,573 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:14] "GET /odoo HTTP/1.1" 200 - 24 0.010 0.027
2025-06-27 05:30:14,923 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:14] "GET /web/assets/2fe85b7/web.assets_web.min.css HTTP/1.1" 200 - 3 0.003 0.009
2025-06-27 05:30:14,924 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:14] "GET /web/assets/029520b/web.assets_web.min.js HTTP/1.1" 200 - 3 0.004 0.008
2025-06-27 05:30:15,026 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 28 0.059 0.054
2025-06-27 05:30:15,177 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "GET /web/assets/f58a84d/web.assets_web_print.min.css HTTP/1.1" 200 - 3 0.001 0.005
2025-06-27 05:30:15,341 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.006
2025-06-27 05:30:15,672 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "POST /web/action/load HTTP/1.1" 200 - 6 0.002 0.009
2025-06-27 05:30:15,791 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.009 0.010
2025-06-27 05:30:15,834 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.013 0.031
2025-06-27 05:30:15,838 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.022 0.028
2025-06-27 05:30:15,838 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "POST /mail/data HTTP/1.1" 200 - 25 0.047 0.022
2025-06-27 05:30:15,870 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:15] "POST /mail/data HTTP/1.1" 200 - 39 0.063 0.034
2025-06-27 05:30:16,059 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:16] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.003 0.006
2025-06-27 05:30:16,182 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:16] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 4 0.001 0.006
2025-06-27 05:30:16,505 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:16] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.005
2025-06-27 05:30:17,742 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:17] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.004
2025-06-27 05:30:51,644 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:51] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.005
2025-06-27 05:30:51,905 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:51] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.001 0.007
2025-06-27 05:30:51,978 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:51] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.004
2025-06-27 05:30:52,275 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:52] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.029 0.023
2025-06-27 05:30:52,286 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:52] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.057
2025-06-27 05:30:53,797 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.016 0.020
2025-06-27 05:30:54,149 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:54] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.007 0.032
2025-06-27 05:30:58,959 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:58] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.040 0.015
2025-06-27 05:30:59,276 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 05:30:59] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.005
2025-06-27 05:32:24,557 14928 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:32:24,557 14928 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:32:24,557 14928 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:32:24,557 14928 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:32:24,748 14928 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:32:24,763 14928 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:32:25,110 14928 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:32:25,110 14928 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:32:25,161 14928 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:32:25,167 14928 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:32:25,290 14928 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:32:27,302 14928 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:32:27,306 14928 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:32:27,665 14928 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.38s, 0 queries (+0 extra) 
2025-06-27 05:32:27,890 14928 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:32:27,896 14928 INFO ecomplus odoo.modules.registry: Registry loaded in 2.784s 
2025-06-27 05:32:41,158 14928 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1956: DeprecationWarning: Deprecated model.clear_cache(), use registry.clear_cache() instead
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 119, in shell
    self.console(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 83, in console
    return getattr(self, shell)(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 103, in python
    Console(locals=local_vars).interact()
  File "C:\Python312\Lib\code.py", line 241, in interact
    more = self.push(line)
  File "C:\Python312\Lib\code.py", line 267, in push
    more = self.runsource(source, self.filename)
  File "C:\Python312\Lib\code.py", line 74, in runsource
    self.runcode(code)
  File "C:\Python312\Lib\code.py", line 90, in runcode
    exec(code, self.locals)
  File "<console>", line 1, in <module>
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 1956, in clear_caches
    warnings.warn("Deprecated model.clear_cache(), use registry.clear_cache() instead", DeprecationWarning)
 
2025-06-27 05:32:41,158 14928 INFO ecomplus odoo.modules.registry: Invalidating all model caches from clear_caches C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1957 
2025-06-27 05:32:56,000 24544 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:32:56,000 24544 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:32:56,001 24544 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:32:56,001 24544 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:32:56,176 24544 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:32:56,191 24544 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:32:56,464 24544 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 05:32:56,464 24544 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 05:32:56,514 24544 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:32:56,520 24544 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:32:56,681 24544 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:32:58,566 24544 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:32:58,570 24544 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 112, in shell
    registry = Registry(dbname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:32:58,956 24544 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.27s, 0 queries (+0 extra) 
2025-06-27 05:32:59,178 24544 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:32:59,183 24544 INFO ecomplus odoo.modules.registry: Registry loaded in 2.718s 
2025-06-27 05:33:14,572 24544 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1956: DeprecationWarning: Deprecated model.clear_cache(), use registry.clear_cache() instead
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 119, in shell
    self.console(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 83, in console
    return getattr(self, shell)(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 103, in python
    Console(locals=local_vars).interact()
  File "C:\Python312\Lib\code.py", line 241, in interact
    more = self.push(line)
  File "C:\Python312\Lib\code.py", line 267, in push
    more = self.runsource(source, self.filename)
  File "C:\Python312\Lib\code.py", line 74, in runsource
    self.runcode(code)
  File "C:\Python312\Lib\code.py", line 90, in runcode
    exec(code, self.locals)
  File "<console>", line 1, in <module>
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 1956, in clear_caches
    warnings.warn("Deprecated model.clear_cache(), use registry.clear_cache() instead", DeprecationWarning)
 
2025-06-27 05:33:14,573 24544 INFO ecomplus odoo.modules.registry: Invalidating all model caches from clear_caches C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1957 
2025-06-27 05:33:16,265 24544 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1956: DeprecationWarning: Deprecated model.clear_cache(), use registry.clear_cache() instead
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 126, in run
    self.shell(config['db_name'])
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 119, in shell
    self.console(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 83, in console
    return getattr(self, shell)(local_vars)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\shell.py", line 103, in python
    Console(locals=local_vars).interact()
  File "C:\Python312\Lib\code.py", line 241, in interact
    more = self.push(line)
  File "C:\Python312\Lib\code.py", line 267, in push
    more = self.runsource(source, self.filename)
  File "C:\Python312\Lib\code.py", line 74, in runsource
    self.runcode(code)
  File "C:\Python312\Lib\code.py", line 90, in runcode
    exec(code, self.locals)
  File "<console>", line 5, in <module>
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 1956, in clear_caches
    warnings.warn("Deprecated model.clear_cache(), use registry.clear_cache() instead", DeprecationWarning)
 
2025-06-27 05:33:16,265 24544 INFO ecomplus odoo.modules.registry: Invalidating all model caches from clear_caches C:\Program Files\Odoo 18.0.********\server\odoo\models.py:1957 
2025-06-27 05:33:36,888 25900 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 05:33:36,889 25900 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 05:33:36,889 25900 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 05:33:36,889 25900 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 05:33:37,214 25900 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 05:33:37,227 25900 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 05:33:37,527 25900 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 05:33:38,794 25900 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 05:33:38,843 25900 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 05:33:38,848 25900 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 05:33:38,963 25900 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 05:33:41,054 25900 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:33:41,056 25900 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 05:33:41,407 25900 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.44s, 0 queries (+0 extra) 
2025-06-27 05:33:41,640 25900 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 05:33:41,645 25900 INFO ecomplus odoo.modules.registry: Registry loaded in 2.849s 
2025-06-27 05:43:41,926 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-27 05:43:41,939 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.013s 
2025-06-27 05:43:41,943 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-27 05:43:42,032 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-27 06:04:36,393 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-27 06:04:36,412 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.018s 
2025-06-27 06:04:36,416 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-27 06:04:36,421 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-27 06:20:28,736 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-27 06:20:28,755 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.018s 
2025-06-27 06:20:28,759 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-27 06:20:28,767 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-27 06:20:28,776 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-27 06:20:28,787 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.011s 
2025-06-27 06:20:28,789 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-27 06:20:28,794 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-27 06:20:28,805 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-27 06:20:28,812 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.008s 
2025-06-27 06:20:28,815 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-27 06:20:28,820 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-27 06:20:28,831 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-27 06:20:28,846 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.015s 
2025-06-27 06:20:28,849 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-27 06:20:28,854 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-27 06:20:28,862 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-27 06:20:28,865 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-27 06:20:28,868 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-27 06:20:28,873 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-27 06:29:38,062 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-27 06:29:38,073 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.012s 
2025-06-27 06:29:38,076 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-27 06:29:38,079 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-27 06:29:38,084 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-27 06:29:38,113 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.026s 
2025-06-27 06:29:38,115 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-27 06:29:38,118 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-27 06:36:40,843 29716 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xs86__dd9U7sa2s_N1d0amQC9a2Hhdxu0LyiSU3Iih) 
2025-06-27 06:36:40,866 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 06:36:40] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.003 0.049
2025-06-27 06:36:41,239 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 06:36:41] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.001 0.048
2025-06-27 06:43:42,755 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-27 06:43:42,769 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.014s 
2025-06-27 06:43:42,771 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-27 06:43:42,774 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-27 07:04:36,774 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-27 07:04:36,794 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.020s 
2025-06-27 07:04:36,797 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-27 07:04:36,801 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-27 07:20:29,182 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-27 07:20:29,188 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.006s 
2025-06-27 07:20:29,190 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-27 07:20:29,195 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-27 07:20:29,201 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-27 07:20:29,209 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.007s 
2025-06-27 07:20:29,211 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-27 07:20:29,215 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-27 07:20:29,222 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-27 07:20:29,226 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.004s 
2025-06-27 07:20:29,230 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-27 07:20:29,233 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-27 07:20:29,240 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-27 07:20:29,248 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.008s 
2025-06-27 07:20:29,251 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-27 07:20:29,254 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-27 07:20:29,260 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-27 07:20:29,263 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-27 07:20:29,266 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-27 07:20:29,271 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-27 07:29:38,510 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) starting 
2025-06-27 07:29:38,520 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) done in 0.010s 
2025-06-27 07:29:38,522 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) processed 0 records, 0 records remaining 
2025-06-27 07:29:38,528 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'eCommerce: send email to customers about their abandoned cart' (38) completed 
2025-06-27 07:29:38,536 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) starting 
2025-06-27 07:29:38,555 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) done in 0.019s 
2025-06-27 07:29:38,559 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) processed 0 records, 0 records remaining 
2025-06-27 07:29:38,562 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Product: send email regarding products availability' (39) completed 
2025-06-27 07:43:43,435 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-27 07:43:43,445 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.010s 
2025-06-27 07:43:43,450 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-27 07:43:43,453 25900 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-27 08:04:37,039 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) starting 
2025-06-27 08:04:37,053 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) done in 0.013s 
2025-06-27 08:04:37,057 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) processed 0 records, 0 records remaining 
2025-06-27 08:04:37,062 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'CRM: enrich leads (IAP)' (33) completed 
2025-06-27 08:05:58,500 29716 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xs86__dd9U7sa2s_N1d0amQC9a2Hhdxu0LyiSU3Iih) 
2025-06-27 08:05:58,505 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:05:58] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.003 0.009
2025-06-27 08:05:58,870 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:05:58] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.007 0.005
2025-06-27 08:20:32,037 29716 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (U4WZSmSVUMrxcKpHaAv_tkIpkUD3h5xz9B4r8Cl9MW) 
2025-06-27 08:20:32,408 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:20:32] "GET / HTTP/1.1" 200 - 24 0.068 0.344
2025-06-27 08:20:48,972 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) starting 
2025-06-27 08:20:48,988 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) done in 0.015s 
2025-06-27 08:20:48,990 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-27 08:20:48,997 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-27 08:20:49,006 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-27 08:20:49,022 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.015s 
2025-06-27 08:20:49,025 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-27 08:20:49,030 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-27 08:20:49,039 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-27 08:20:49,047 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.009s 
2025-06-27 08:20:49,049 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-27 08:20:49,056 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-27 08:20:49,067 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-27 08:20:49,091 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.024s 
2025-06-27 08:20:49,094 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-27 08:20:49,098 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-27 08:20:49,105 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-27 08:20:49,108 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-27 08:20:49,112 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-27 08:20:49,116 29716 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-27 08:27:45,360 29716 INFO ? odoo.modules.registry: Reloading the model registry after database signaling. 
2025-06-27 08:27:45,402 29716 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 08:27:45,482 29716 INFO ? odoo.modules.loading: 1 modules loaded in 0.08s, 0 queries (+0 extra) 
2025-06-27 08:27:45,535 29716 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 08:27:45,775 29716 INFO ? odoo.modules.loading: 176 modules loaded in 0.24s, 0 queries (+0 extra) 
2025-06-27 08:27:46,634 29716 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 08:27:46,642 29716 INFO ? odoo.modules.registry: Registry loaded in 1.282s 
2025-06-27 08:27:46,645 29716 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 08:27:48,660 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:48] "GET /odoo/apps HTTP/1.1" 200 - 139 0.120 3.196
2025-06-27 08:27:49,441 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:49] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.007 0.007
2025-06-27 08:27:49,798 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:49] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.011
2025-06-27 08:27:50,121 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.022 0.190
2025-06-27 08:27:50,123 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /mail/data HTTP/1.1" 200 - 43 0.087 0.130
2025-06-27 08:27:50,188 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 496 0.280 1.097
2025-06-27 08:27:50,189 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.046 0.025
2025-06-27 08:27:50,192 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.041 0.215
2025-06-27 08:27:50,251 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.057 0.273
2025-06-27 08:27:50,434 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 08:27:50,615 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 4 0.005 0.030
2025-06-27 08:27:50,643 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 61 0.033 0.031
2025-06-27 08:27:50,679 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.004 0.096
2025-06-27 08:27:50,958 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:50] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.009
2025-06-27 08:27:52,721 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:52] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.018
2025-06-27 08:27:56,899 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:56] "POST /web/action/load HTTP/1.1" 200 - 10 0.004 0.008
2025-06-27 08:27:57,347 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:57] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.121
2025-06-27 08:27:57,401 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:57] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.038 0.137
2025-06-27 08:27:57,413 29716 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:27:57] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.009 0.177
2025-06-27 08:28:04,044 29716 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 08:28:04,045 29716 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 08:28:05,060 29716 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 7 connections  
2025-06-27 08:28:11,926 36148 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 08:28:11,927 36148 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 08:28:11,927 36148 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 08:28:11,927 36148 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 08:28:12,175 36148 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 08:28:12,192 36148 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 08:28:13,927 36148 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 08:31:59,453 36148 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 08:31:59,453 36148 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 08:31:59,824 36148 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-27 08:32:02,118 31816 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 08:32:02,118 31816 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 08:32:02,118 31816 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 08:32:02,118 31816 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 08:32:02,285 31816 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 08:32:02,301 31816 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 08:32:03,886 31816 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 08:34:52,824 31816 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 08:34:52,832 31816 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 08:34:52,988 31816 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 08:34:55,832 31816 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 08:34:55,838 31816 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 08:34:56,266 31816 INFO ? odoo.modules.loading: 176 modules loaded in 3.28s, 0 queries (+0 extra) 
2025-06-27 08:34:56,512 31816 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 08:34:56,520 31816 INFO ? odoo.modules.registry: Registry loaded in 3.745s 
2025-06-27 08:34:56,523 31816 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 08:34:56,696 31816 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:34:56] "GET /web/service-worker.js HTTP/1.1" 200 - 9 0.022 3.900
2025-06-27 08:35:02,576 31816 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:35:02] "GET /odoo HTTP/1.1" 200 - 139 0.111 9.693
2025-06-27 08:35:03,478 31816 INFO ecomplus odoo.sql_db: Connection to the database failed 
2025-06-27 08:35:03,478 31816 WARNING ecomplus odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\base\models\ir_cron.py", line 132, in _process_jobs
    with db.cursor() as cron_cr:
         ^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 796, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 288, in __init__
    self._cnx = pool.borrow(dsn)
                ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 723, in borrow
    result = psycopg2.connect(
             ^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\psycopg2\__init__.py", line 122, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  the database system is shutting down

2025-06-27 08:35:03,550 31816 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 08:35:03,550 31816 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 08:35:03,850 31816 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 4 connections  
2025-06-27 08:35:47,804 9476 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 08:35:47,804 9476 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 08:35:47,804 9476 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 08:35:47,804 9476 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 08:35:49,039 9476 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 08:35:49,101 9476 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 08:35:50,957 9476 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 08:36:08,132 9476 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 08:36:08,139 9476 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 08:36:11,335 9476 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 08:36:20,532 9476 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 08:36:20,537 9476 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 08:36:21,303 9476 INFO ? odoo.modules.loading: 176 modules loaded in 9.97s, 0 queries (+0 extra) 
2025-06-27 08:36:21,603 9476 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 08:36:21,626 9476 INFO ? odoo.modules.registry: Registry loaded in 13.705s 
2025-06-27 08:36:21,642 9476 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 08:36:25,786 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:25] "GET / HTTP/1.1" 200 - 246 0.806 17.080
2025-06-27 08:36:34,320 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 200 - 11 0.016 8.095
2025-06-27 08:36:34,344 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 200 - 9 0.021 8.115
2025-06-27 08:36:34,350 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 200 - 9 0.026 8.116
2025-06-27 08:36:34,356 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 200 - 9 0.024 8.123
2025-06-27 08:36:34,356 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 200 - 9 0.026 8.122
2025-06-27 08:36:34,356 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:34] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 200 - 9 0.027 8.120
2025-06-27 08:36:36,002 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:36] "GET /@/ HTTP/1.1" 303 - 1 0.000 0.000
2025-06-27 08:36:42,155 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:42] "GET /odoo/action-website.website_preview?path=/ HTTP/1.1" 200 - 80 0.079 6.075
2025-06-27 08:36:42,652 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:42] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.022 0.011
2025-06-27 08:36:42,982 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:42] "POST /web/action/load HTTP/1.1" 200 - 9 0.012 0.019
2025-06-27 08:36:43,150 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 1 0.000 0.000
2025-06-27 08:36:43,220 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.017 0.054
2025-06-27 08:36:43,233 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.013
2025-06-27 08:36:43,233 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 3 0.013 0.000
2025-06-27 08:36:43,300 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-06-27 08:36:43,400 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /mail/data HTTP/1.1" 200 - 43 0.231 0.019
2025-06-27 08:36:43,467 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-06-27 08:36:43,549 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET /website/force/1?path=/ HTTP/1.1" 303 - 1 0.000 0.008
2025-06-27 08:36:43,613 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.028 0.045
2025-06-27 08:36:43,652 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET /website/iframefallback HTTP/1.1" 200 - 19 0.002 0.109
2025-06-27 08:36:43,835 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.017 0.017
2025-06-27 08:36:43,891 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET / HTTP/1.1" 200 - 23 0.028 0.062
2025-06-27 08:36:43,957 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:43] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 4 0.044 0.046
2025-06-27 08:36:44,426 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:44] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.001 0.005
2025-06-27 08:36:44,776 9476 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 08:36:44,965 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:44] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 19 0.010 0.253
2025-06-27 08:36:45,491 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:36:45] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.032
2025-06-27 08:37:55,747 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:55] "POST /web/action/load HTTP/1.1" 200 - 10 0.175 0.021
2025-06-27 08:37:56,254 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:56] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.316 0.056
2025-06-27 08:37:56,581 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.003 0.003
2025-06-27 08:37:56,652 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:56] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 61 0.049 0.028
2025-06-27 08:37:56,733 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:56] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.011 0.146
2025-06-27 08:37:59,573 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:59] "POST /web/action/load HTTP/1.1" 200 - 9 0.005 0.005
2025-06-27 08:37:59,929 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:37:59] "POST /web/dataset/call_kw/connection.api/get_views HTTP/1.1" 200 - 16 0.003 0.018
2025-06-27 08:38:00,174 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:00] "POST /web/dataset/call_kw/connection.api/web_search_read HTTP/1.1" 200 - 4 0.005 0.000
2025-06-27 08:38:00,243 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:00] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.000
2025-06-27 08:38:02,372 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:02] "POST /web/action/load HTTP/1.1" 200 - 6 0.006 0.005
2025-06-27 08:38:02,884 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:02] "POST /mail/data HTTP/1.1" 200 - 52 0.134 0.026
2025-06-27 08:38:02,984 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:02] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.000 0.009
2025-06-27 08:38:04,617 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:04] "POST /web/action/load HTTP/1.1" 200 - 12 0.024 0.006
2025-06-27 08:38:05,142 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:05] "POST /web/action/run HTTP/1.1" 200 - 21 0.033 0.179
2025-06-27 08:38:05,321 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:05] "POST /web/dataset/call_kw/crm.lead/get_views HTTP/1.1" 200 - 71 0.054 0.072
2025-06-27 08:38:05,524 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:05] "POST /web/dataset/call_kw/crm.lead/read_progress_bar HTTP/1.1" 200 - 6 0.043 0.006
2025-06-27 08:38:05,655 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:05] "POST /web/dataset/call_kw/crm.lead/web_read_group HTTP/1.1" 200 - 5 0.004 0.006
2025-06-27 08:38:05,742 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:05] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.000 0.003
2025-06-27 08:38:06,222 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:06] "GET /web/image/res.users/5/avatar_128 HTTP/1.1" 200 - 8 0.014 0.064
2025-06-27 08:38:06,223 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:06] "GET /web/image/res.users/4/avatar_128 HTTP/1.1" 200 - 8 0.015 0.065
2025-06-27 08:38:06,230 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:06] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.054 0.031
2025-06-27 08:38:06,231 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:06] "GET /web/image/res.users/1/avatar_128 HTTP/1.1" 304 - 9 0.052 0.033
2025-06-27 08:38:06,237 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:06] "GET /web/image/res.users/3/avatar_128 HTTP/1.1" 200 - 8 0.055 0.039
2025-06-27 08:38:07,558 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:07] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.181
2025-06-27 08:38:08,125 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:08] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 135 0.092 0.153
2025-06-27 08:38:08,213 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:08] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 20 0.047 0.033
2025-06-27 08:38:24,977 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:24] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 08:38:25,338 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:25] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.025 0.022
2025-06-27 08:38:25,348 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:38:25] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.051
2025-06-27 08:40:33,569 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:33] "POST /web/action/load HTTP/1.1" 200 - 6 0.005 0.011
2025-06-27 08:40:33,812 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:33] "POST /web/dataset/call_kw/website/get_current_website HTTP/1.1" 200 - 1 0.000 0.006
2025-06-27 08:40:33,913 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:33] "POST /web/dataset/call_kw/website/search_read HTTP/1.1" 200 - 2 0.003 0.006
2025-06-27 08:40:33,964 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:33] "GET /website/force/1?path=/ HTTP/1.1" 303 - 1 0.000 0.007
2025-06-27 08:40:34,312 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:34] "GET /website/iframefallback HTTP/1.1" 200 - 3 0.038 0.011
2025-06-27 08:40:35,184 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:35] "GET / HTTP/1.1" 200 - 27 0.576 0.317
2025-06-27 08:40:36,673 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:36] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 1 0.001 0.004
2025-06-27 08:40:36,680 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:36] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.028 0.016
2025-06-27 08:40:36,877 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:36] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 13 0.011 0.231
2025-06-27 08:40:44,281 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /shop HTTP/1.1" 200 - 151 0.246 0.676
2025-06-27 08:40:44,809 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /web/image/product.template/6/image_512/Pizza%20Margherita?unique=92adb9b HTTP/1.1" 200 - 6 0.027 0.040
2025-06-27 08:40:44,814 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /web/image/product.template/5/image_512/Cheese%20Burger?unique=92adb9b HTTP/1.1" 200 - 11 0.026 0.042
2025-06-27 08:40:44,820 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /web/image/product.template/4/image_512/Bacon%20Burger?unique=92adb9b HTTP/1.1" 200 - 6 0.033 0.045
2025-06-27 08:40:44,823 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /web/image/product.template/80/image_512/12314?unique=92adb9b HTTP/1.1" 200 - 10 0.037 0.042
2025-06-27 08:40:44,829 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:44] "GET /web/image/product.template/78/image_512/123?unique=92adb9b HTTP/1.1" 200 - 5 0.036 0.052
2025-06-27 08:40:45,147 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/71/image_512/shopee?unique=92adb9b HTTP/1.1" 200 - 5 0.005 0.014
2025-06-27 08:40:45,187 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/19/image_512/Coca-Cola?unique=92adb9b HTTP/1.1" 200 - 6 0.009 0.024
2025-06-27 08:40:45,194 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/20/image_512/Water?unique=92adb9b HTTP/1.1" 200 - 6 0.006 0.023
2025-06-27 08:40:45,465 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/7/image_512/Pizza%20Vegetarian?unique=92adb9b HTTP/1.1" 200 - 6 0.004 0.008
2025-06-27 08:40:45,485 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/8/image_512/Pasta%204%20Formaggi?unique=92adb9b HTTP/1.1" 200 - 6 0.005 0.013
2025-06-27 08:40:45,515 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/9/image_512/Funghi?unique=92adb9b HTTP/1.1" 200 - 6 0.006 0.013
2025-06-27 08:40:45,556 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/image/product.template/83/image_512/Event%20Registration?unique=92adb9b HTTP/1.1" 200 - 5 0.008 0.029
2025-06-27 08:40:45,791 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 3 0.002 0.006
2025-06-27 08:40:45,822 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.013 0.016
2025-06-27 08:40:45,988 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:45] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 1 0.001 0.004
2025-06-27 08:40:46,005 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:46] "GET /web/image/product.template/10/image_512/Pasta%20Bolognese?unique=92adb9b HTTP/1.1" 200 - 6 0.006 0.014
2025-06-27 08:40:46,046 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:46] "GET /web/image/product.template/11/image_512/Chicken%20Curry%20Sandwich?unique=92adb9b HTTP/1.1" 200 - 6 0.008 0.016
2025-06-27 08:40:53,455 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:53] "GET /event HTTP/1.1" 200 - 75 0.069 0.247
2025-06-27 08:40:53,950 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:53] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 3 0.003 0.011
2025-06-27 08:40:53,961 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:53] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 1 0.001 0.007
2025-06-27 08:40:53,965 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:53] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.018 0.010
2025-06-27 08:40:56,094 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /slides HTTP/1.1" 200 - 79 0.090 0.396
2025-06-27 08:40:56,458 9476 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /website_slides/static/src/img/banner_default.svg HTTP/1.1" 200 - 0 0.000 0.016
2025-06-27 08:40:56,506 9476 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /website_slides_survey/static/src/img/certification.svg HTTP/1.1" 200 - 0 0.000 0.031
2025-06-27 08:40:56,510 9476 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /gamification/static/img/rank_doctor_badge.svg HTTP/1.1" 200 - 0 0.000 0.031
2025-06-27 08:40:56,526 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /web/image/gamification.karma.rank/4/image_128?unique=4ffdc2e HTTP/1.1" 200 - 6 0.014 0.035
2025-06-27 08:40:56,532 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /profile/avatar/2?field=avatar_128 HTTP/1.1" 200 - 15 0.024 0.032
2025-06-27 08:40:56,776 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 3 0.002 0.006
2025-06-27 08:40:56,835 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 1 0.001 0.005
2025-06-27 08:40:56,859 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:40:56] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.015 0.014
2025-06-27 08:41:02,165 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:02] "GET / HTTP/1.1" 200 - 27 0.024 0.061
2025-06-27 08:41:02,397 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:02] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 3 0.002 0.008
2025-06-27 08:41:02,694 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:02] "GET /web/bundle/website.assets_all_wysiwyg?lang=vi_VN&debug=1&website_id=1 HTTP/1.1" 200 - 1 0.001 0.007
2025-06-27 08:41:02,712 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:02] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.014 0.017
2025-06-27 08:41:02,737 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:02] "GET /profile/avatar/2?field=avatar_128 HTTP/1.1" 304 - 8 0.007 0.019
2025-06-27 08:41:13,049 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 8 0.019 0.017
2025-06-27 08:41:13,050 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 8 0.017 0.019
2025-06-27 08:41:13,052 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 8 0.009 0.029
2025-06-27 08:41:13,093 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 8 0.017 0.020
2025-06-27 08:41:13,095 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 8 0.016 0.020
2025-06-27 08:41:13,099 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:13] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 8 0.014 0.025
2025-06-27 08:41:22,357 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:22] "POST /web/action/load HTTP/1.1" 200 - 10 0.007 0.011
2025-06-27 08:41:22,670 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:22] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 16 0.006 0.059
2025-06-27 08:41:22,706 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:22] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 2 0.002 0.004
2025-06-27 08:41:23,014 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:23] "GET /web/image/res.users/3/avatar_128 HTTP/1.1" 304 - 8 0.006 0.013
2025-06-27 08:41:23,171 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:23] "GET /web/image/res.users/4/avatar_128 HTTP/1.1" 304 - 8 0.018 0.019
2025-06-27 08:41:23,174 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:23] "GET /web/image/res.users/5/avatar_128 HTTP/1.1" 304 - 8 0.024 0.017
2025-06-27 08:41:23,179 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:23] "GET /web/image/res.users/1/avatar_128 HTTP/1.1" 304 - 9 0.019 0.026
2025-06-27 08:41:23,183 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:23] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.022 0.025
2025-06-27 08:41:25,270 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:25] "POST /web/dataset/call_kw/sale.order/onchange HTTP/1.1" 200 - 23 0.030 0.300
2025-06-27 08:41:25,701 9476 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 08:41:25] "GET /partner_autocomplete/static/lib/jsvat.js HTTP/1.1" 200 - 0 0.000 0.026
2025-06-27 08:43:51,178 9476 WARNING ecomplus odoo.addons.base.models.ir_cron: Skipping database ecomplus because of modules to install/upgrade/remove. 
2025-06-27 08:43:59,523 9476 WARNING ecomplus odoo.addons.base.models.ir_cron: Skipping database ecomplus because of modules to install/upgrade/remove. 
2025-06-27 08:44:51,199 9476 INFO ecomplus odoo.modules.registry: Reloading the model registry after database signaling. 
2025-06-27 08:44:51,213 9476 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 08:44:51,223 9476 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 08:44:51,239 9476 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 08:44:51,324 9476 INFO ecomplus odoo.modules.loading: 176 modules loaded in 0.09s, 0 queries (+0 extra) 
2025-06-27 08:44:51,633 9476 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 08:44:51,642 9476 INFO ecomplus odoo.modules.registry: Registry loaded in 0.443s 
2025-06-27 08:44:51,643 9476 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (False) starting 
2025-06-27 08:44:51,652 9476 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (False) done in 0.008s 
2025-06-27 08:44:51,655 9476 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-27 08:44:51,659 9476 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-27 08:45:21,125 9476 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 08:45:22,655 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:22] "GET / HTTP/1.1" 200 - 236 0.133 1.403
2025-06-27 08:45:23,173 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:23] "POST /im_livechat/init HTTP/1.1" 200 - 22 0.016 0.013
2025-06-27 08:45:29,570 9476 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/431878c/im_livechat.assets_embed_external.min.css (id:1562) 
2025-06-27 08:45:29,575 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:29] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 20 0.026 6.407
2025-06-27 08:45:34,656 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:34] "POST /im_livechat/get_session HTTP/1.1" 200 - 22 0.016 0.038
2025-06-27 08:45:43,988 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:43] "POST /im_livechat/get_session HTTP/1.1" 200 - 69 0.045 0.132
2025-06-27 08:45:44,384 9476 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/398bcd5/bus.websocket_worker_assets.min.js (id:1563) 
2025-06-27 08:45:44,387 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:44] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 8 0.006 0.059
2025-06-27 08:45:44,388 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:44] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 16 0.013 0.017
2025-06-27 08:45:44,640 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:44] "POST /mail/data HTTP/1.1" 200 - 47 0.036 0.019
2025-06-27 08:45:44,718 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:44] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.001 0.008
2025-06-27 08:45:44,725 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:44] "GET /web/bundle/web.assets_emoji?lang=vi_VN&website_id=1 HTTP/1.1" 200 - 5 0.007 0.007
2025-06-27 08:45:45,283 9476 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/dae9ea0/web.assets_emoji.min.js (id:1564) 
2025-06-27 08:45:45,286 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:45] "GET /web/assets/1/dae9ea0/web.assets_emoji.min.js HTTP/1.1" 200 - 9 0.004 0.325
2025-06-27 08:45:45,442 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:45] "POST /mail/message/post HTTP/1.1" 200 - 38 0.028 0.067
2025-06-27 08:45:45,468 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:45] "POST /web/dataset/call_kw/discuss.channel/channel_fetched HTTP/1.1" 200 - 10 0.007 0.007
2025-06-27 08:45:45,774 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:45] "POST /web/dataset/call_kw/discuss.channel/channel_fetched HTTP/1.1" 200 - 5 0.003 0.004
2025-06-27 08:45:52,867 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:52] "GET /odoo HTTP/1.1" 200 - 69 0.028 1.457
2025-06-27 08:45:53,270 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "GET /bus/websocket_worker_bundle?v=18.0-5 HTTP/1.1" 304 - 3 0.003 0.004
2025-06-27 08:45:53,662 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "POST /web/action/load HTTP/1.1" 200 - 6 0.008 0.008
2025-06-27 08:45:53,684 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.010 0.011
2025-06-27 08:45:53,723 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.002 0.032
2025-06-27 08:45:53,724 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.018 0.025
2025-06-27 08:45:53,725 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.019 0.022
2025-06-27 08:45:53,775 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "POST /mail/data HTTP/1.1" 200 - 61 0.092 0.037
2025-06-27 08:45:53,990 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:53] "POST /mail/data HTTP/1.1" 200 - 50 0.030 0.049
2025-06-27 08:45:54,115 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:54] "POST /mail/inbox/messages HTTP/1.1" 200 - 4 0.005 0.050
2025-06-27 08:45:55,780 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:45:55] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.033
2025-06-27 08:46:01,225 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:01] "POST /web/action/load HTTP/1.1" 200 - 9 0.006 0.006
2025-06-27 08:46:01,561 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:01] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 3 0.004 0.006
2025-06-27 08:46:01,623 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:01] "POST /discuss/channel/messages HTTP/1.1" 200 - 22 0.021 0.020
2025-06-27 08:46:01,698 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:01] "POST /web/dataset/call_kw/im_livechat.channel/get_views HTTP/1.1" 200 - 86 0.072 0.062
2025-06-27 08:46:01,836 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:01] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 12 0.008 0.012
2025-06-27 08:46:02,033 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:02] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.006 0.010
2025-06-27 08:46:03,621 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:03] "POST /web/action/load HTTP/1.1" 200 - 12 0.006 0.008
2025-06-27 08:46:03,989 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:03] "POST /web/dataset/call_kw/discuss.channel/get_views HTTP/1.1" 200 - 32 0.013 0.031
2025-06-27 08:46:04,206 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:04] "POST /web/dataset/call_kw/discuss.channel/web_search_read HTTP/1.1" 200 - 8 0.004 0.008
2025-06-27 08:46:04,321 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:04] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.006 0.011
2025-06-27 08:46:05,559 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:05] "POST /discuss/channel/members HTTP/1.1" 200 - 4 0.002 0.004
2025-06-27 08:46:13,208 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:13] "POST /discuss/channel/notify_typing HTTP/1.1" 200 - 14 0.008 0.062
2025-06-27 08:46:16,383 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:16] "GET /web/bundle/web.assets_emoji?lang=vi_VN HTTP/1.1" 200 - 2 0.001 0.004
2025-06-27 08:46:16,747 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:16] "POST /discuss/channel/notify_typing HTTP/1.1" 200 - 14 0.010 0.054
2025-06-27 08:46:16,752 9476 INFO ecomplus odoo.addons.base.models.assetsbundle: Found a similar attachment for /web/assets/dae9ea0/web.assets_emoji.min.js, copying from /web/assets/1/dae9ea0/web.assets_emoji.min.js 
2025-06-27 08:46:16,762 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:16] "GET /web/assets/dae9ea0/web.assets_emoji.min.js HTTP/1.1" 200 - 10 0.010 0.053
2025-06-27 08:46:17,259 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:17] "POST /mail/message/post HTTP/1.1" 200 - 36 0.036 0.073
2025-06-27 08:46:17,422 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:17] "POST /web/dataset/call_kw/discuss.channel/channel_fetched HTTP/1.1" 200 - 5 0.002 0.006
2025-06-27 08:46:17,624 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:46:17] "POST /web/dataset/call_kw/discuss.channel/channel_fetched HTTP/1.1" 200 - 5 0.004 0.005
2025-06-27 08:47:28,407 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:28] "POST /discuss/channel/messages HTTP/1.1" 200 - 22 0.011 0.025
2025-06-27 08:47:33,336 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:33] "GET / HTTP/1.1" 200 - 29 0.020 0.073
2025-06-27 08:47:33,766 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:33] "GET /web/image/website.s_reference_demo_image_1 HTTP/1.1" 304 - 10 0.008 0.013
2025-06-27 08:47:33,788 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:33] "GET /web/image/website.s_reference_demo_image_2 HTTP/1.1" 304 - 9 0.012 0.014
2025-06-27 08:47:33,792 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:33] "GET /web/image/website.s_reference_demo_image_3 HTTP/1.1" 304 - 9 0.008 0.021
2025-06-27 08:47:34,021 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "GET /web/image/website.s_reference_demo_image_4 HTTP/1.1" 304 - 9 0.005 0.006
2025-06-27 08:47:34,123 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "GET /web/image/website.s_reference_demo_image_5 HTTP/1.1" 304 - 9 0.010 0.009
2025-06-27 08:47:34,123 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "GET /web/image/website.s_reference_default_image_6 HTTP/1.1" 304 - 9 0.005 0.013
2025-06-27 08:47:34,276 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 3 0.001 0.016
2025-06-27 08:47:34,583 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "POST /im_livechat/init HTTP/1.1" 200 - 15 0.006 0.011
2025-06-27 08:47:34,912 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.001 0.008
2025-06-27 08:47:34,953 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:34] "POST /mail/data HTTP/1.1" 200 - 33 0.029 0.020
2025-06-27 08:47:35,167 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:35] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.001 0.003
2025-06-27 08:47:35,301 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:35] "POST /discuss/channel/messages HTTP/1.1" 200 - 22 0.013 0.061
2025-06-27 08:47:53,493 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 6 0.003 0.007
2025-06-27 08:47:53,858 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:53] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 61 0.040 0.019
2025-06-27 08:47:53,900 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:53] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.005 0.095
2025-06-27 08:47:55,410 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:55] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.005
2025-06-27 08:47:55,704 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:55] "POST /web/dataset/call_kw/link.tracker/get_views HTTP/1.1" 200 - 23 0.025 0.019
2025-06-27 08:47:55,748 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:55] "POST /web/dataset/call_kw/link.tracker/web_search_read HTTP/1.1" 200 - 3 0.006 0.004
2025-06-27 08:47:58,055 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:58] "POST /web/action/load HTTP/1.1" 200 - 9 0.003 0.009
2025-06-27 08:47:58,436 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:58] "POST /web/dataset/call_kw/im_livechat.channel/get_views HTTP/1.1" 200 - 36 0.014 0.035
2025-06-27 08:47:58,669 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:47:58] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 11 0.003 0.014
2025-06-27 08:48:00,163 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:00] "POST /web/action/load HTTP/1.1" 200 - 11 0.002 0.008
2025-06-27 08:48:00,490 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:00] "POST /web/dataset/call_kw/discuss.channel/get_views HTTP/1.1" 200 - 2 0.000 0.011
2025-06-27 08:48:00,756 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:00] "POST /web/dataset/call_kw/discuss.channel/web_search_read HTTP/1.1" 200 - 9 0.006 0.008
2025-06-27 08:48:00,831 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:00] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.007 0.021
2025-06-27 08:48:05,517 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:05] "POST /web/action/load HTTP/1.1" 200 - 9 0.006 0.006
2025-06-27 08:48:05,862 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:05] "POST /web/dataset/call_kw/chatbot.script/get_views HTTP/1.1" 200 - 21 0.012 0.020
2025-06-27 08:48:06,101 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:06] "POST /web/dataset/call_kw/chatbot.script/web_search_read HTTP/1.1" 200 - 3 0.002 0.006
2025-06-27 08:48:12,218 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:12] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 11 0.007 0.012
2025-06-27 08:48:14,013 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "POST /web/action/load HTTP/1.1" 200 - 9 0.005 0.005
2025-06-27 08:48:14,327 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "POST /web/dataset/call_kw/website.visitor/get_views HTTP/1.1" 200 - 34 0.019 0.037
2025-06-27 08:48:14,527 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "POST /web/dataset/call_kw/website.visitor/web_search_read HTTP/1.1" 200 - 25 0.023 0.169
2025-06-27 08:48:14,868 9476 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "GET /base/static/img/avatar_grey.png HTTP/1.1" 200 - 0 0.000 0.004
2025-06-27 08:48:14,885 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "GET /web/image/res.partner/3/image_128?unique=1751013943000 HTTP/1.1" 200 - 6 0.010 0.010
2025-06-27 08:48:14,889 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:14] "GET /web/image/res.partner/3/avatar_128?unique=1751013943000 HTTP/1.1" 200 - 8 0.005 0.017
2025-06-27 08:48:19,126 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:19] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.008
2025-06-27 08:48:19,475 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:19] "POST /web/dataset/call_kw/mail.canned.response/get_views HTTP/1.1" 200 - 17 0.009 0.014
2025-06-27 08:48:19,714 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:19] "POST /web/dataset/call_kw/mail.canned.response/web_search_read HTTP/1.1" 200 - 6 0.007 0.005
2025-06-27 08:48:24,725 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:24] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 11 0.010 0.012
2025-06-27 08:48:30,191 9476 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 08:48:30] "POST /mail/data HTTP/1.1" 200 - 14 0.007 0.011
2025-06-27 08:49:20,601 9476 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 08:49:20,601 9476 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 08:49:20,926 9476 ERROR ecomplus odoo.sql_db: bad query: b' UPDATE "bus_presence"\n                    SET "status" = "__tmp"."status"::VARCHAR\n                    FROM (VALUES (3, \'offline\')) AS "__tmp"("id", "status")\n                    WHERE "bus_presence"."id" = "__tmp"."id"\n                '
ERROR: could not serialize access due to concurrent update
 
2025-06-27 08:49:20,962 9476 ERROR ecomplus odoo.addons.bus.websocket: could not serialize access due to concurrent update
 
Traceback (most recent call last):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 318, in get_messages
    message = self._process_next_message()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 464, in _process_next_message
    self._handle_control_frame(frame)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 602, in _handle_control_frame
    self._terminate()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\bus\websocket.py", line 583, in _terminate
    with acquire_cursor(self._db) as cr:
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 201, in __exit__
    self.commit()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 479, in commit
    self.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 159, in flush
    self.transaction.flush()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 1022, in flush
    env_to_flush.flush_all()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 849, in flush_all
    self[model_name].flush_model()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6752, in flush_model
    self._flush(fnames)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 6830, in _flush
    model.browse(some_ids)._write_multi(vals_list)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\models.py", line 4916, in _write_multi
    self.env.execute_query(SQL(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 978, in execute_query
    self.cr.execute(query)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\sql_db.py", line 354, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.SerializationFailure: could not serialize access due to concurrent update

2025-06-27 08:49:21,626 9476 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 8 connections  
2025-06-27 08:49:28,868 28616 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 08:49:28,868 28616 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 08:49:28,868 28616 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 08:49:28,869 28616 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 08:49:29,134 28616 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 08:49:29,152 28616 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 08:49:31,004 28616 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 09:03:19,957 28616 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 09:03:19,957 28616 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 09:03:20,357 28616 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 2 connections  
2025-06-27 09:03:22,796 6488 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 09:03:22,796 6488 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 09:03:22,797 6488 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 09:03:22,797 6488 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 09:03:22,973 6488 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 09:03:22,987 6488 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 09:03:24,704 6488 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 09:09:09,999 6488 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 09:09:10,005 6488 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:09:10,110 6488 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 09:09:12,188 6488 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:09:12,202 6488 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:09:12,589 6488 INFO ? odoo.modules.loading: 176 modules loaded in 2.48s, 0 queries (+0 extra) 
2025-06-27 09:09:12,818 6488 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 09:09:12,826 6488 INFO ? odoo.modules.registry: Registry loaded in 2.878s 
2025-06-27 09:09:12,830 6488 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:09:12,893 6488 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xs86__dd9U7sa2s_N1d0amQC9a2Hhdxu0LyiSU3Iih) 
2025-06-27 09:09:12,893 6488 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xs86__dd9U7sa2s_N1d0amQC9a2Hhdxu0LyiSU3Iih) 
2025-06-27 09:09:12,933 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:12] "GET /web/service-worker.js HTTP/1.1" 200 - 25 0.044 2.943
2025-06-27 09:09:16,665 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:16] "GET /odoo/apps HTTP/1.1" 200 - 121 0.135 6.585
2025-06-27 09:09:17,826 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:17] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.000 0.006
2025-06-27 09:09:17,853 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:17] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.014 0.011
2025-06-27 09:09:17,865 6488 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 09:09:18,152 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:18] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.001 0.003
2025-06-27 09:09:18,685 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:18] "POST /mail/data HTTP/1.1" 200 - 70 0.131 0.080
2025-06-27 09:09:20,008 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:20] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:09:23,057 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:23] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 16 0.054 3.946
2025-06-27 09:09:23,095 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:23] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.016 5.508
2025-06-27 09:09:24,158 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:24] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 496 0.290 7.167
2025-06-27 09:09:28,021 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:28] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 2 0.002 0.004
2025-06-27 09:09:28,376 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:09:28] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.000 0.004
2025-06-27 09:12:19,320 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /discuss/channel/messages HTTP/1.1" 200 - 28 0.031 0.026
2025-06-27 09:12:19,338 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.007 0.050
2025-06-27 09:12:19,348 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /web/action/load HTTP/1.1" 200 - 11 0.019 0.050
2025-06-27 09:12:19,369 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 13 0.041 0.048
2025-06-27 09:12:19,600 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 56 0.032 0.041
2025-06-27 09:12:19,701 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.002
2025-06-27 09:12:19,980 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 61 0.035 0.026
2025-06-27 09:12:19,991 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:19] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.007 0.065
2025-06-27 09:12:21,561 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:21] "GET /odoo/apps HTTP/1.1" 200 - 23 0.020 0.023
2025-06-27 09:12:21,919 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:21] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 24 0.040 0.048
2025-06-27 09:12:22,372 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/action/load HTTP/1.1" 200 - 11 0.006 0.006
2025-06-27 09:12:22,535 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.003 0.061
2025-06-27 09:12:22,535 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.009 0.056
2025-06-27 09:12:22,535 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /mail/data HTTP/1.1" 200 - 45 0.042 0.038
2025-06-27 09:12:22,553 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.013 0.052
2025-06-27 09:12:22,554 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.009 0.054
2025-06-27 09:12:22,691 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:12:22,883 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/hr.employee/search_read HTTP/1.1" 200 - 3 0.011 0.010
2025-06-27 09:12:22,941 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.062 0.020
2025-06-27 09:12:22,942 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.012 0.009
2025-06-27 09:12:22,950 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /discuss/channel/messages HTTP/1.1" 200 - 22 0.048 0.024
2025-06-27 09:12:22,965 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:22] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.006 0.100
2025-06-27 09:12:24,848 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:24] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.003
2025-06-27 09:12:31,207 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:31] "POST /discuss/channel/fold HTTP/1.1" 200 - 16 0.012 0.051
2025-06-27 09:12:50,432 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:50] "GET /web/session/logout HTTP/1.1" 303 - 1 0.001 0.007
2025-06-27 09:12:50,748 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:50] "GET /odoo HTTP/1.1" 303 - 4 0.003 0.004
2025-06-27 09:12:51,538 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:51] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 122 0.129 0.341
2025-06-27 09:12:51,779 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:51] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 30 0.020 0.151
2025-06-27 09:12:56,706 6488 INFO ecomplus odoo.addons.base.models.res_users: Login successful for db:ecomplus login:admin from 127.0.0.1 
2025-06-27 09:12:56,717 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:56] "POST /web/login HTTP/1.1" 303 - 28 0.062 0.662
2025-06-27 09:12:56,733 6488 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (xhuR8ZjePpqFhfHFGtpOBYDInQ9l4KZ6QdyJEWq6JI) 
2025-06-27 09:12:56,762 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:56] "GET /odoo HTTP/1.1" 200 - 24 0.013 0.027
2025-06-27 09:12:56,857 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:56] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 24 0.017 0.053
2025-06-27 09:12:57,349 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "POST /web/action/load HTTP/1.1" 200 - 6 0.002 0.007
2025-06-27 09:12:57,472 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.008 0.012
2025-06-27 09:12:57,495 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.013 0.012
2025-06-27 09:12:57,512 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "POST /mail/data HTTP/1.1" 200 - 25 0.040 0.022
2025-06-27 09:12:57,528 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.036 0.019
2025-06-27 09:12:57,568 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "POST /mail/data HTTP/1.1" 200 - 50 0.071 0.043
2025-06-27 09:12:57,727 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "POST /mail/inbox/messages HTTP/1.1" 200 - 4 0.004 0.007
2025-06-27 09:12:57,883 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:12:57] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.003 0.007
2025-06-27 09:13:00,570 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:00] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.001 0.005
2025-06-27 09:13:00,577 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:00] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.001 0.004
2025-06-27 09:13:00,882 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:00] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.000 0.006
2025-06-27 09:13:06,407 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:06] "POST /web/action/load HTTP/1.1" 200 - 10 0.035 0.083
2025-06-27 09:13:06,699 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:06] "POST /web/dataset/call_kw/event.event/get_views HTTP/1.1" 200 - 88 0.086 0.075
2025-06-27 09:13:06,738 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:06] "POST /web/dataset/call_kw/event.event/web_read_group HTTP/1.1" 200 - 9 0.008 0.005
2025-06-27 09:13:07,038 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:07] "GET /web/image/res.users/3/avatar_128 HTTP/1.1" 304 - 8 0.007 0.007
2025-06-27 09:13:07,204 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:07] "GET /web/image/res.users/5/avatar_128 HTTP/1.1" 304 - 8 0.019 0.020
2025-06-27 09:13:07,211 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:07] "GET /web/image/res.users/4/avatar_128 HTTP/1.1" 304 - 8 0.020 0.025
2025-06-27 09:13:07,213 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:07] "GET /web/image/res.users/1/avatar_128 HTTP/1.1" 304 - 9 0.026 0.022
2025-06-27 09:13:07,216 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:07] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 9 0.020 0.028
2025-06-27 09:13:08,825 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:08] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.004
2025-06-27 09:13:09,223 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:09] "POST /web/dataset/call_kw/mailing.mailing/get_views HTTP/1.1" 200 - 43 0.026 0.044
2025-06-27 09:13:09,424 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:09] "POST /web/dataset/call_kw/mailing.mailing/web_search_read HTTP/1.1" 200 - 3 0.007 0.003
2025-06-27 09:13:09,542 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:09] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.004
2025-06-27 09:13:10,168 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:10] "POST /web/action/load HTTP/1.1" 200 - 10 0.042 0.010
2025-06-27 09:13:10,566 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:10] "POST /web/dataset/call_kw/slide.channel/get_views HTTP/1.1" 200 - 54 0.029 0.050
2025-06-27 09:13:10,758 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:10] "POST /web/dataset/call_kw/slide.channel/web_search_read HTTP/1.1" 200 - 2 0.003 0.005
2025-06-27 09:13:14,239 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:14] "POST /web/action/load HTTP/1.1" 200 - 10 0.004 0.009
2025-06-27 09:13:14,579 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:14] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 2 0.001 0.009
2025-06-27 09:13:14,836 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:14] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.003
2025-06-27 09:13:14,953 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:14] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.036 0.021
2025-06-27 09:13:14,959 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:14] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.006 0.057
2025-06-27 09:13:16,995 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:16] "POST /web/action/load HTTP/1.1" 200 - 9 0.005 0.005
2025-06-27 09:13:17,428 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:17] "POST /web/dataset/call_kw/im_livechat.channel/get_views HTTP/1.1" 200 - 79 0.034 0.067
2025-06-27 09:13:17,592 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:17] "POST /web/dataset/call_kw/im_livechat.channel/web_search_read HTTP/1.1" 200 - 11 0.009 0.009
2025-06-27 09:13:23,714 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:23] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.005
2025-06-27 09:13:24,082 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:24] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.034 0.023
2025-06-27 09:13:24,084 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:24] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.053
2025-06-27 09:13:25,728 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:25] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.016 0.021
2025-06-27 09:13:26,003 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:26] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.005 0.040
2025-06-27 09:13:27,917 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:27] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.030 0.013
2025-06-27 09:13:28,298 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:28] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.006 0.058
2025-06-27 09:13:28,499 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:28] "GET /account/static/description/l10n.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 09:13:28,624 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:28] "GET /muk_web_colors/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 09:13:29,663 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:29] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.012 0.024
2025-06-27 09:13:30,013 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:30] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.003 0.029
2025-06-27 09:13:32,886 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:32] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 60 0.031 0.025
2025-06-27 09:13:33,215 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:33] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.003 0.006
2025-06-27 09:13:36,953 6488 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:13:36,954 6488 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:13:36,954 6488 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:13:37,694 6488 WARNING ecomplus odoo.modules.module: python external dependency on 'hashlib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:13:37,694 6488 WARNING ecomplus odoo.modules.module: python external dependency on 'urllib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:13:37,701 6488 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-06-27 09:13:38,147 6488 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 09:13:38,153 6488 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:13:38,171 6488 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-27 09:13:38,173 6488 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-27 09:13:38,963 6488 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 09:13:39,031 6488 INFO ecomplus odoo.modules.loading: Loading module multichannel_ecommerce (128/176) 
2025-06-27 09:13:39,497 6488 INFO ecomplus odoo.modules.registry: module multichannel_ecommerce: creating or updating database tables 
2025-06-27 09:13:39,670 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/multichannel_security.xml 
2025-06-27 09:13:39,767 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/ir.model.access.csv 
2025-06-27 09:13:39,788 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/platform_data.xml 
2025-06-27 09:13:39,797 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/sequence_data.xml 
2025-06-27 09:13:39,803 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/multichannel_shop_views.xml 
2025-06-27 09:13:39,875 6488 WARNING ecomplus odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-06-27 09:13:39,875 6488 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-cube) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 30,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:13:39,876 6488 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-shopping-cart) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 33,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:13:39,876 6488 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-money) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 38,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:13:39,901 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/product_mapping_views.xml 
2025-06-27 09:13:39,918 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/transaction_views.xml 
2025-06-27 09:13:39,935 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/advertising_views.xml 
2025-06-27 09:13:39,954 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/affiliate_views.xml 
2025-06-27 09:13:39,972 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/warehouse_mapping_views.xml 
2025-06-27 09:13:39,988 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/menu_views.xml 
2025-06-27 09:13:40,029 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/oauth_templates.xml 
2025-06-27 09:13:40,048 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/dashboard_templates.xml 
2025-06-27 09:13:40,055 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/shop_sync_wizard_views.xml 
2025-06-27 09:13:40,065 6488 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/product_sync_wizard_views.xml 
2025-06-27 09:13:40,082 6488 INFO ecomplus odoo.addons.base.models.ir_module: module multichannel_ecommerce: no translation for language vi_VN 
2025-06-27 09:13:40,137 6488 WARNING ecomplus odoo.modules.loading: The models ['multichannel.shop.sync.wizard', 'multichannel.product.sync.wizard'] have no access rules in module multichannel_ecommerce, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
multichannel_ecommerce.access_multichannel_shop_sync_wizard,access_multichannel_shop_sync_wizard,multichannel_ecommerce.model_multichannel_shop_sync_wizard,base.group_user,1,0,0,0
multichannel_ecommerce.access_multichannel_product_sync_wizard,access_multichannel_product_sync_wizard,multichannel_ecommerce.model_multichannel_product_sync_wizard,base.group_user,1,0,0,0 
2025-06-27 09:13:40,148 6488 INFO ecomplus odoo.modules.loading: Module multichannel_ecommerce loaded in 1.12s, 594 queries (+594 other) 
2025-06-27 09:13:40,162 6488 INFO ecomplus odoo.modules.loading: 176 modules loaded in 1.20s, 594 queries (+594 extra) 
2025-06-27 09:13:41,800 6488 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 09:13:41,833 6488 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-27 09:13:41,834 6488 INFO ecomplus odoo.modules.registry: Registry loaded in 3.705s 
2025-06-27 09:13:41,834 6488 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-27 09:13:41,841 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:41] "POST /web/dataset/call_button/ir.module.module/button_immediate_upgrade HTTP/1.1" 200 - 5839 2.335 2.559
2025-06-27 09:13:42,168 6488 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:13:43,588 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:43] "GET /odoo HTTP/1.1" 200 - 115 0.064 1.360
2025-06-27 09:13:43,998 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:43] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.010 0.008
2025-06-27 09:13:44,421 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "POST /web/action/load HTTP/1.1" 200 - 7 0.012 0.012
2025-06-27 09:13:44,472 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.013 0.059
2025-06-27 09:13:44,483 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.026 0.035
2025-06-27 09:13:44,487 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.034 0.023
2025-06-27 09:13:44,543 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "POST /mail/data HTTP/1.1" 200 - 43 0.114 0.030
2025-06-27 09:13:44,734 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 497 0.263 0.839
2025-06-27 09:13:44,879 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:44] "POST /mail/data HTTP/1.1" 200 - 66 0.037 0.039
2025-06-27 09:13:45,321 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:13:45] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.014 0.040
2025-06-27 09:15:24,909 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-27 09:15:24,964 6488 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 6 checked, 6 removed 
2025-06-27 09:15:25,040 6488 INFO ecomplus odoo.addons.base.models.res_users: GC'd 1 user log entries 
2025-06-27 09:15:25,055 6488 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-27 09:15:25,062 6488 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 1 entries 
2025-06-27 09:15:25,064 6488 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:15:25,091 6488 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-27 09:15:25,105 6488 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1609] 
2025-06-27 09:15:25,315 6488 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-27 09:15:25,901 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.993s 
2025-06-27 09:15:25,906 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-27 09:15:25,911 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-27 09:16:44,986 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:16:44] "GET / HTTP/1.1" 200 - 35 0.030 0.361
2025-06-27 09:17:00,039 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:00] "GET /odoo HTTP/1.1" 200 - 42 0.034 0.090
2025-06-27 09:17:00,358 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:00] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.006 0.010
2025-06-27 09:17:00,672 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:00] "POST /web/action/load HTTP/1.1" 200 - 6 0.008 0.005
2025-06-27 09:17:00,687 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:00] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.005 0.007
2025-06-27 09:17:00,717 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:00] "POST /mail/data HTTP/1.1" 200 - 25 0.037 0.020
2025-06-27 09:17:01,082 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:01] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.011 0.007
2025-06-27 09:17:01,099 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:01] "POST /mail/data HTTP/1.1" 200 - 49 0.020 0.048
2025-06-27 09:17:01,291 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:01] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.002 0.004
2025-06-27 09:17:02,734 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:02] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:17:09,646 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:09] "POST /web/action/load HTTP/1.1" 200 - 9 0.002 0.008
2025-06-27 09:17:11,439 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:11] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 461 0.213 1.342
2025-06-27 09:17:11,800 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:11] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 306 0.184 0.148
2025-06-27 09:17:12,293 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.001 0.004
2025-06-27 09:17:12,312 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "POST /base_setup/data HTTP/1.1" 200 - 6 0.004 0.003
2025-06-27 09:17:12,539 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "GET /base/static/description/settings.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 09:17:12,630 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "GET /event/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 09:17:12,642 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "GET /web/static/img/mobile_app_qrcode_android.svg HTTP/1.1" 200 - 0 0.000 0.013
2025-06-27 09:17:12,688 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:12] "GET /web/static/img/mobile_app_qrcode_ios.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-27 09:17:24,709 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:24] "GET /odoo/settings?debug=1 HTTP/1.1" 200 - 23 0.011 0.022
2025-06-27 09:17:25,204 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 319 0.099 0.128
2025-06-27 09:17:25,227 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.004 0.008
2025-06-27 09:17:25,243 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "POST /mail/data HTTP/1.1" 200 - 25 0.016 0.019
2025-06-27 09:17:25,608 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.009 0.013
2025-06-27 09:17:25,609 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.009
2025-06-27 09:17:25,612 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:25] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.011 0.016
2025-06-27 09:17:26,067 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:26] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 261 0.065 0.169
2025-06-27 09:17:26,282 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:26] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 112 0.087 0.102
2025-06-27 09:17:26,789 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:26] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.002 0.003
2025-06-27 09:17:26,803 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:26] "POST /base_setup/data HTTP/1.1" 200 - 5 0.001 0.003
2025-06-27 09:17:27,174 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:27] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.002
2025-06-27 09:17:36,785 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:36] "POST /web/dataset/call_kw/ir.rule/has_access HTTP/1.1" 200 - 1 0.002 0.005
2025-06-27 09:17:36,786 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:36] "POST /web/dataset/call_kw/ir.ui.view/has_access HTTP/1.1" 200 - 1 0.001 0.007
2025-06-27 09:17:36,787 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:36] "POST /web/dataset/call_kw/ir.model.access/has_access HTTP/1.1" 200 - 1 0.001 0.006
2025-06-27 09:17:38,496 6488 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-06-27 09:17:38,498 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:38] "GET /web/become HTTP/1.1" 303 - 7 0.003 0.009
2025-06-27 09:17:38,991 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:38] "GET /odoo HTTP/1.1" 200 - 75 0.071 0.123
2025-06-27 09:17:39,476 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.003 0.017
2025-06-27 09:17:39,491 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.017 0.018
2025-06-27 09:17:39,493 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "POST /mail/data HTTP/1.1" 200 - 21 0.026 0.013
2025-06-27 09:17:39,579 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /web/webclient/load_menus/cb583d8ddc9de95bd887cf4f9c9d74c366b439757aadac65d9dba4dc378c10b8 HTTP/1.1" 200 - 327 0.129 0.120
2025-06-27 09:17:39,586 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.000 0.006
2025-06-27 09:17:39,805 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.002 0.007
2025-06-27 09:17:39,823 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "POST /web/action/load HTTP/1.1" 200 - 6 0.013 0.012
2025-06-27 09:17:39,982 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.011 0.017
2025-06-27 09:17:39,985 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /web/image/res.partner/2/avatar_128?unique=1750972583000 HTTP/1.1" 200 - 6 0.008 0.022
2025-06-27 09:17:39,986 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:39] "GET /web/image?model=res.users&field=avatar_128&id=1 HTTP/1.1" 200 - 7 0.013 0.020
2025-06-27 09:17:40,015 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:40] "POST /mail/data HTTP/1.1" 200 - 37 0.039 0.025
2025-06-27 09:17:40,189 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:40] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.002 0.003
2025-06-27 09:17:41,671 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:41] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.002
2025-06-27 09:17:43,416 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:43] "POST /web/action/load HTTP/1.1" 200 - 9 0.003 0.009
2025-06-27 09:17:43,735 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:43] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 75 0.021 0.048
2025-06-27 09:17:43,753 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:43] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.002 0.004
2025-06-27 09:17:44,142 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:44] "GET /web/static/img/smiling_face.svg HTTP/1.1" 200 - 0 0.000 0.012
2025-06-27 09:17:46,895 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:46] "POST /web/dataset/call_kw/multichannel.shop/onchange HTTP/1.1" 200 - 4 0.002 0.008
2025-06-27 09:17:59,483 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:17:59] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.000 0.004
2025-06-27 09:18:01,630 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:18:01] "POST /web/action/load HTTP/1.1" 200 - 9 0.005 0.008
2025-06-27 09:18:01,973 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:18:01] "POST /web/dataset/call_kw/multichannel.product.mapping/get_views HTTP/1.1" 200 - 16 0.007 0.014
2025-06-27 09:18:02,275 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:18:02] "POST /web/dataset/call_kw/multichannel.product.mapping/web_search_read HTTP/1.1" 200 - 2 0.059 0.003
2025-06-27 09:18:02,295 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:18:02] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:20:26,009 6488 INFO ecomplus odoo.modules.registry: Reloading the model registry after database signaling. 
2025-06-27 09:20:26,022 6488 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 09:20:26,028 6488 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:20:26,042 6488 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 09:20:26,120 6488 INFO ecomplus odoo.modules.loading: 176 modules loaded in 0.08s, 0 queries (+0 extra) 
2025-06-27 09:20:26,485 6488 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 09:20:26,494 6488 INFO ecomplus odoo.modules.registry: Registry loaded in 0.485s 
2025-06-27 09:20:26,495 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (False) starting 
2025-06-27 09:20:26,499 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (False) done in 0.004s 
2025-06-27 09:20:26,538 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) processed 0 records, 0 records remaining 
2025-06-27 09:20:26,542 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Notification: Notify scheduled messages' (8) completed 
2025-06-27 09:20:26,583 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) starting 
2025-06-27 09:20:26,593 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) done in 0.010s 
2025-06-27 09:20:26,595 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) processed 0 records, 0 records remaining 
2025-06-27 09:20:26,598 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Mail: Email Queue Manager' (3) completed 
2025-06-27 09:20:26,604 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) starting 
2025-06-27 09:20:26,607 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) done in 0.003s 
2025-06-27 09:20:26,609 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) processed 0 records, 0 records remaining 
2025-06-27 09:20:26,612 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Partner Autocomplete: Sync with remote DB' (13) completed 
2025-06-27 09:20:36,199 6488 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:20:37,516 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:37] "GET /odoo/discuss HTTP/1.1" 200 - 124 0.066 1.253
2025-06-27 09:20:37,997 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:37] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.007 0.009
2025-06-27 09:20:38,495 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "POST /web/action/load HTTP/1.1" 200 - 8 0.029 0.064
2025-06-27 09:20:38,497 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.016 0.055
2025-06-27 09:20:38,522 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.052 0.040
2025-06-27 09:20:38,529 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.022 0.061
2025-06-27 09:20:38,617 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "POST /mail/data HTTP/1.1" 200 - 43 0.161 0.051
2025-06-27 09:20:38,674 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 497 0.237 0.894
2025-06-27 09:20:38,940 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:38] "POST /mail/data HTTP/1.1" 200 - 66 0.036 0.041
2025-06-27 09:20:39,367 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:39] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.003 0.037
2025-06-27 09:20:42,262 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) starting 
2025-06-27 09:20:42,266 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) done in 0.003s 
2025-06-27 09:20:42,268 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) processed 0 records, 0 records remaining 
2025-06-27 09:20:42,271 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'SMS: SMS Queue Manager' (14) completed 
2025-06-27 09:20:42,277 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) starting 
2025-06-27 09:20:42,283 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) done in 0.005s 
2025-06-27 09:20:42,286 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) processed 0 records, 0 records remaining 
2025-06-27 09:20:42,289 6488 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Snailmail: process letters queue' (15) completed 
2025-06-27 09:20:44,618 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:44] "GET /odoo/discuss HTTP/1.1" 200 - 23 0.011 0.022
2025-06-27 09:20:45,023 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:45] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 1 0.001 0.045
2025-06-27 09:20:45,141 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:45] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 30 0.076 0.087
2025-06-27 09:20:45,320 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:45] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 319 0.206 0.167
2025-06-27 09:20:58,032 6488 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/2fe85b7/web.assets_web.min.css (id:1566) 
2025-06-27 09:20:58,090 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:20:58] "GET /web/assets/2fe85b7/web.assets_web.min.css HTTP/1.1" 200 - 31 0.045 13.145
2025-06-27 09:21:06,097 6488 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/f58a84d/web.assets_web_print.min.css (id:1567) 
2025-06-27 09:21:06,110 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:06] "GET /web/assets/f58a84d/web.assets_web_print.min.css HTTP/1.1" 200 - 25 0.038 7.669
2025-06-27 09:21:28,863 6488 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/029520b/web.assets_web.min.js (id:1568) 
2025-06-27 09:21:29,560 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:29] "GET /web/assets/029520b/web.assets_web.min.js HTTP/1.1" 200 - 28 0.020 44.594
2025-06-27 09:21:30,165 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 1 0.000 0.038
2025-06-27 09:21:30,246 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.005 0.005
2025-06-27 09:21:30,257 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.021
2025-06-27 09:21:30,397 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.009
2025-06-27 09:21:30,633 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "POST /web/action/load HTTP/1.1" 200 - 7 0.015 0.009
2025-06-27 09:21:30,643 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.010 0.023
2025-06-27 09:21:30,646 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.016 0.020
2025-06-27 09:21:30,651 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.042
2025-06-27 09:21:30,662 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "POST /mail/data HTTP/1.1" 200 - 25 0.030 0.023
2025-06-27 09:21:30,786 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:30] "POST /mail/data HTTP/1.1" 200 - 49 0.034 0.052
2025-06-27 09:21:31,015 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:31] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.003 0.004
2025-06-27 09:21:31,184 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:21:31] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.003 0.005
2025-06-27 09:22:58,199 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:22:58] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.047
2025-06-27 09:29:55,352 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:55] "POST /web/action/load HTTP/1.1" 200 - 9 0.007 0.016
2025-06-27 09:29:57,263 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:57] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 461 0.314 1.082
2025-06-27 09:29:57,658 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:57] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 322 0.169 0.172
2025-06-27 09:29:58,236 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:58] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.002 0.003
2025-06-27 09:29:58,268 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:58] "POST /base_setup/data HTTP/1.1" 200 - 6 0.003 0.003
2025-06-27 09:29:58,495 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:58] "GET /web/static/img/mobile_app_qrcode_android.svg HTTP/1.1" 200 - 0 0.000 0.004
2025-06-27 09:29:58,656 6488 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:29:58] "GET /web/static/img/mobile_app_qrcode_ios.svg HTTP/1.1" 200 - 0 0.000 0.002
2025-06-27 09:32:37,193 26220 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 09:32:37,193 26220 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 09:32:37,194 26220 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 09:32:37,194 26220 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 09:32:37,425 26220 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 09:32:37,445 26220 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 09:32:37,852 26220 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 09:32:39,123 26220 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 09:32:39,179 26220 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 09:32:39,185 26220 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:32:39,205 26220 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-27 09:32:39,209 26220 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-06-27 09:32:40,927 26220 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Multi-Channel E-commerce Connector'] to user __system__ #1 via n/a 
2025-06-27 09:32:40,928 26220 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Multi-Channel E-commerce Connector'] to user __system__ #1 via n/a 
2025-06-27 09:32:41,700 26220 WARNING ecomplus odoo.modules.module: python external dependency on 'hashlib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:32:41,700 26220 WARNING ecomplus odoo.modules.module: python external dependency on 'urllib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:32:41,703 26220 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-06-27 09:32:42,101 26220 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 09:32:43,697 26220 INFO ecomplus odoo.modules.loading: Loading module multichannel_ecommerce (128/176) 
2025-06-27 09:32:43,919 26220 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:32:43,921 26220 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:32:44,091 26220 INFO ecomplus odoo.modules.registry: module multichannel_ecommerce: creating or updating database tables 
2025-06-27 09:32:44,256 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/multichannel_security.xml 
2025-06-27 09:32:44,353 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/ir.model.access.csv 
2025-06-27 09:32:44,380 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/platform_data.xml 
2025-06-27 09:32:44,389 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/sequence_data.xml 
2025-06-27 09:32:44,395 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/multichannel_shop_views.xml 
2025-06-27 09:32:44,441 26220 WARNING ecomplus odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-06-27 09:32:44,442 26220 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-cube) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 30,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:32:44,442 26220 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-shopping-cart) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 33,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:32:44,443 26220 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-money) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 38,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:32:44,462 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/product_mapping_views.xml 
2025-06-27 09:32:44,480 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/transaction_views.xml 
2025-06-27 09:32:44,497 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/advertising_views.xml 
2025-06-27 09:32:44,513 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/affiliate_views.xml 
2025-06-27 09:32:44,535 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/warehouse_mapping_views.xml 
2025-06-27 09:32:44,552 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/menu_views.xml 
2025-06-27 09:32:44,640 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/oauth_templates.xml 
2025-06-27 09:32:44,661 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/dashboard_templates.xml 
2025-06-27 09:32:44,669 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/shop_sync_wizard_views.xml 
2025-06-27 09:32:44,680 26220 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/product_sync_wizard_views.xml 
2025-06-27 09:32:44,697 26220 INFO ecomplus odoo.addons.base.models.ir_module: module multichannel_ecommerce: no translation for language vi_VN 
2025-06-27 09:32:44,754 26220 WARNING ecomplus odoo.modules.loading: The models ['multichannel.shop.sync.wizard', 'multichannel.product.sync.wizard'] have no access rules in module multichannel_ecommerce, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
multichannel_ecommerce.access_multichannel_shop_sync_wizard,access_multichannel_shop_sync_wizard,multichannel_ecommerce.model_multichannel_shop_sync_wizard,base.group_user,1,0,0,0
multichannel_ecommerce.access_multichannel_product_sync_wizard,access_multichannel_product_sync_wizard,multichannel_ecommerce.model_multichannel_product_sync_wizard,base.group_user,1,0,0,0 
2025-06-27 09:32:44,762 26220 INFO ecomplus odoo.modules.loading: Module multichannel_ecommerce loaded in 1.07s, 640 queries (+640 other) 
2025-06-27 09:32:45,072 26220 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.97s, 640 queries (+640 extra) 
2025-06-27 09:32:46,698 26220 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 09:32:46,704 26220 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-27 09:32:46,705 26220 INFO ecomplus odoo.modules.registry: Registry loaded in 7.580s 
2025-06-27 09:33:46,867 26220 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-27 09:33:46,914 26220 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 3 checked, 0 removed 
2025-06-27 09:33:46,924 26220 INFO ecomplus odoo.models.unlink: User #1 deleted ir.cron.progress records with IDs: [939, 940, 941, 942, 943] 
2025-06-27 09:33:46,980 26220 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-27 09:33:46,990 26220 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-27 09:33:46,995 26220 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:33:46,998 26220 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:33:47,014 26220 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-27 09:33:47,023 26220 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1613, 1614, 1615, 1616, 1617] 
2025-06-27 09:33:47,150 26220 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-27 09:33:47,468 26220 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.601s 
2025-06-27 09:33:47,471 26220 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-27 09:33:47,475 26220 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-27 09:34:21,188 26220 INFO ecomplus odoo.service.server: Initiating shutdown 
2025-06-27 09:34:21,188 26220 INFO ecomplus odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 09:34:21,720 26220 INFO ecomplus odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 5 connections  
2025-06-27 09:34:34,798 8184 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 09:34:34,798 8184 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 09:34:34,798 8184 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 09:34:34,798 8184 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 09:34:34,990 8184 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 09:34:35,003 8184 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 09:34:35,298 8184 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-06-27 09:34:36,583 8184 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 09:34:36,642 8184 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 09:34:36,648 8184 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:34:36,667 8184 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-27 09:34:36,669 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-06-27 09:34:37,702 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Multi-Channel E-commerce Connector'] to user __system__ #1 via n/a 
2025-06-27 09:34:37,702 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Multi-Channel E-commerce Connector'] to user __system__ #1 via n/a 
2025-06-27 09:34:38,291 8184 WARNING ecomplus odoo.modules.module: python external dependency on 'hashlib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:34:38,292 8184 WARNING ecomplus odoo.modules.module: python external dependency on 'urllib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:34:38,294 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-06-27 09:34:38,663 8184 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 09:34:39,940 8184 INFO ecomplus odoo.modules.loading: Loading module multichannel_ecommerce (128/176) 
2025-06-27 09:34:40,139 8184 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:34:40,142 8184 WARNING ecomplus py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\server\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 182, in run
    main(args)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\cli\server.py", line 175, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1424, in start
    rc = server.run(preload, stop)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 589, in run
    rc = preload_registries(preload)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\service\server.py", line 1328, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:34:40,306 8184 INFO ecomplus odoo.modules.registry: module multichannel_ecommerce: creating or updating database tables 
2025-06-27 09:34:40,467 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/multichannel_security.xml 
2025-06-27 09:34:40,546 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/ir.model.access.csv 
2025-06-27 09:34:40,571 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/platform_data.xml 
2025-06-27 09:34:40,578 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/sequence_data.xml 
2025-06-27 09:34:40,585 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/multichannel_shop_views.xml 
2025-06-27 09:34:40,624 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-06-27 09:34:40,625 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-cube) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 30,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:34:40,625 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-shopping-cart) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 33,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:34:40,626 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-money) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 38,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:34:40,641 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/product_mapping_views.xml 
2025-06-27 09:34:40,656 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/transaction_views.xml 
2025-06-27 09:34:40,669 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/advertising_views.xml 
2025-06-27 09:34:40,686 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/affiliate_views.xml 
2025-06-27 09:34:40,703 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/warehouse_mapping_views.xml 
2025-06-27 09:34:40,719 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/menu_views.xml 
2025-06-27 09:34:40,768 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/oauth_templates.xml 
2025-06-27 09:34:40,782 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/dashboard_templates.xml 
2025-06-27 09:34:40,788 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/shop_sync_wizard_views.xml 
2025-06-27 09:34:40,796 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/product_sync_wizard_views.xml 
2025-06-27 09:34:40,808 8184 INFO ecomplus odoo.addons.base.models.ir_module: module multichannel_ecommerce: no translation for language vi_VN 
2025-06-27 09:34:40,859 8184 WARNING ecomplus odoo.modules.loading: The models ['multichannel.shop.sync.wizard', 'multichannel.product.sync.wizard'] have no access rules in module multichannel_ecommerce, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
multichannel_ecommerce.access_multichannel_shop_sync_wizard,access_multichannel_shop_sync_wizard,multichannel_ecommerce.model_multichannel_shop_sync_wizard,base.group_user,1,0,0,0
multichannel_ecommerce.access_multichannel_product_sync_wizard,access_multichannel_product_sync_wizard,multichannel_ecommerce.model_multichannel_product_sync_wizard,base.group_user,1,0,0,0 
2025-06-27 09:34:40,866 8184 INFO ecomplus odoo.modules.loading: Module multichannel_ecommerce loaded in 0.93s, 628 queries (+628 other) 
2025-06-27 09:34:41,132 8184 INFO ecomplus odoo.modules.loading: 176 modules loaded in 2.47s, 628 queries (+628 extra) 
2025-06-27 09:34:42,600 8184 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 09:34:42,650 8184 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-27 09:34:42,651 8184 INFO ecomplus odoo.modules.registry: Registry loaded in 6.065s 
2025-06-27 09:35:42,783 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-27 09:35:42,828 8184 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-06-27 09:35:42,886 8184 INFO ecomplus odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-06-27 09:35:42,895 8184 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-27 09:35:42,899 8184 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:35:42,901 8184 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:35:42,915 8184 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-27 09:35:43,043 8184 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-27 09:35:43,353 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.571s 
2025-06-27 09:35:43,355 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-27 09:35:43,359 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-27 09:37:14,023 6488 INFO ? odoo.modules.registry: Reloading the model registry after database signaling. 
2025-06-27 09:37:14,069 6488 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 09:37:14,074 6488 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-06-27 09:37:14,091 6488 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 09:37:14,179 6488 INFO ? odoo.modules.loading: 176 modules loaded in 0.09s, 0 queries (+0 extra) 
2025-06-27 09:37:14,555 6488 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 09:37:14,562 6488 INFO ? odoo.modules.registry: Registry loaded in 0.538s 
2025-06-27 09:37:14,563 6488 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:37:14,648 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:14] "GET /web/session/logout HTTP/1.1" 303 - 31 0.033 0.594
2025-06-27 09:37:14,669 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:14] "GET /odoo HTTP/1.1" 303 - 4 0.003 0.014
2025-06-27 09:37:15,427 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:15] "GET /web/login?redirect=/odoo? HTTP/1.1" 200 - 173 0.096 0.657
2025-06-27 09:37:15,960 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:15] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 32 0.023 0.166
2025-06-27 09:37:16,143 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:16] "GET /im_livechat/assets_embed.css HTTP/1.1" 304 - 16 0.005 0.097
2025-06-27 09:37:16,169 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:16] "POST /im_livechat/init HTTP/1.1" 200 - 20 0.009 0.015
2025-06-27 09:37:18,602 6488 INFO ecomplus odoo.addons.base.models.res_users: Login successful for db:ecomplus login:admin from 127.0.0.1 
2025-06-27 09:37:18,616 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:18] "POST /web/login HTTP/1.1" 303 - 38 0.032 0.708
2025-06-27 09:37:18,632 6488 INFO ecomplus odoo.addons.base.models.res_device: User 2 inserts device log (0t8HuG8T63frf7Bhwr_WqfFIh0RrWsu4IQbmCFEJn6) 
2025-06-27 09:37:19,753 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:19] "GET /odoo HTTP/1.1" 200 - 99 0.049 1.083
2025-06-27 09:37:20,359 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:20] "POST /web/action/load HTTP/1.1" 200 - 7 0.009 0.007
2025-06-27 09:37:20,488 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:20] "POST /mail/data HTTP/1.1" 200 - 43 0.034 0.031
2025-06-27 09:37:21,182 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 459 0.248 0.845
2025-06-27 09:37:21,182 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "POST /mail/data HTTP/1.1" 200 - 66 0.096 0.524
2025-06-27 09:37:21,182 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.008 0.614
2025-06-27 09:37:21,219 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.033 0.622
2025-06-27 09:37:21,224 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.024 0.531
2025-06-27 09:37:21,225 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 10 0.022 0.405
2025-06-27 09:37:21,635 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:21] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.005 0.006
2025-06-27 09:37:37,475 6488 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 09:37:37,475 6488 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 09:37:38,544 6488 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 8 connections  
2025-06-27 09:37:39,755 8184 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:37:39,933 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:39] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 8 0.003 0.179
2025-06-27 09:37:39,985 6488 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:39] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.001 0.047
2025-06-27 09:37:40,265 8184 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 09:37:40,310 8184 INFO ecomplus odoo.addons.base.models.res_device: User 1 inserts device log (U4WZSmSVUMrxcKpHaAv_tkIpkUD3h5xz9B4r8Cl9MW) 
2025-06-27 09:37:40,314 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:40] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 3 0.001 0.039
2025-06-27 09:37:40,651 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:40] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.000 0.005
2025-06-27 09:37:42,074 15248 INFO ? odoo: Odoo version 18.0-******** 
2025-06-27 09:37:42,075 15248 INFO ? odoo: Using configuration file at C:\Program Files\Odoo 18.0.********\server\odoo.conf 
2025-06-27 09:37:42,075 15248 INFO ? odoo: addons paths: ['C:\\Program Files\\Odoo 18.0.********\\server\\odoo\\addons', 'c:\\program files\\odoo 18.0.********\\sessions\\addons\\18.0', 'c:\\program files\\odoo 18.0.********\\server\\odoo\\addons'] 
2025-06-27 09:37:42,075 15248 INFO ? odoo: database: openpg@localhost:5432 
2025-06-27 09:37:42,254 15248 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\Program Files\Odoo 18.0.********\thirdparty\wkhtmltopdf.exe 
2025-06-27 09:37:42,269 15248 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-06-27 09:37:43,929 15248 INFO ? odoo.service.server: HTTP service (werkzeug) running on phamthuan:8069 
2025-06-27 09:37:58,644 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:58] "GET /odoo/discuss HTTP/1.1" 200 - 121 0.080 5.100
2025-06-27 09:37:58,952 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:58] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 11 0.015 0.010
2025-06-27 09:37:59,466 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:59] "POST /web/action/load HTTP/1.1" 200 - 8 0.019 0.102
2025-06-27 09:37:59,489 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:59] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.016 0.126
2025-06-27 09:37:59,508 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:59] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.025 0.107
2025-06-27 09:37:59,518 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:59] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 20 0.030 0.128
2025-06-27 09:37:59,566 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:37:59] "POST /mail/data HTTP/1.1" 200 - 48 0.074 0.145
2025-06-27 09:38:00,048 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:00] "POST /mail/data HTTP/1.1" 200 - 84 0.109 0.096
2025-06-27 09:38:00,489 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:00] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.012 0.035
2025-06-27 09:38:00,950 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:00] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 574 0.354 1.927
2025-06-27 09:38:05,039 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:05] "POST /web/action/load HTTP/1.1" 200 - 10 0.006 0.008
2025-06-27 09:38:05,399 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:05] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 64 0.035 0.071
2025-06-27 09:38:05,413 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:05] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:38:05,793 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:05] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 83 0.052 0.028
2025-06-27 09:38:05,819 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:05] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.008 0.090
2025-06-27 09:38:08,866 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:08] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 83 0.036 0.026
2025-06-27 09:38:09,110 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:09] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 8 0.006 0.033
2025-06-27 09:38:20,751 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:20] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 83 0.041 0.023
2025-06-27 09:38:21,087 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 6 0.009 0.005
2025-06-27 09:38:24,254 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:38:24,255 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:38:24,256 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Multi-Channel E-commerce Connector'] to user admin #2 via 127.0.0.1 
2025-06-27 09:38:25,922 8184 WARNING ecomplus odoo.modules.module: python external dependency on 'hashlib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:38:25,923 8184 WARNING ecomplus odoo.modules.module: python external dependency on 'urllib' does not appear o be a valid PyPI package. Using a PyPI package name is recommended. 
2025-06-27 09:38:25,932 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-06-27 09:38:25,998 8184 INFO ecomplus odoo.modules.loading: loading 1 modules... 
2025-06-27 09:38:26,004 8184 INFO ecomplus odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:38:26,025 8184 INFO ecomplus odoo.modules.loading: updating modules list 
2025-06-27 09:38:26,027 8184 INFO ecomplus odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-06-27 09:38:27,113 8184 INFO ecomplus odoo.modules.loading: loading 176 modules... 
2025-06-27 09:38:27,211 8184 INFO ecomplus odoo.modules.loading: Loading module multichannel_ecommerce (128/176) 
2025-06-27 09:38:27,690 8184 INFO ecomplus odoo.modules.registry: module multichannel_ecommerce: creating or updating database tables 
2025-06-27 09:38:27,869 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/multichannel_security.xml 
2025-06-27 09:38:27,982 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/security/ir.model.access.csv 
2025-06-27 09:38:28,003 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/platform_data.xml 
2025-06-27 09:38:28,014 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/data/sequence_data.xml 
2025-06-27 09:38:28,020 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/multichannel_shop_views.xml 
2025-06-27 09:38:28,065 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: 'kanban-box' is deprecated, define a 'card' template instead 
2025-06-27 09:38:28,066 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-cube) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 30,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:38:28,066 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-shopping-cart) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 33,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:38:28,067 8184 WARNING ecomplus odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-money) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\program files\\odoo '
         '18.0.********\\server\\odoo\\addons\\multichannel_ecommerce\\views\\multichannel_shop_views.xml',
 'line': 38,
 'name': 'multichannel.shop.kanban',
 'view': ir.ui.view(3869,),
 'view.model': 'multichannel.shop',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_multichannel_shop_kanban'} 
2025-06-27 09:38:28,086 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/product_mapping_views.xml 
2025-06-27 09:38:28,108 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/transaction_views.xml 
2025-06-27 09:38:28,127 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/advertising_views.xml 
2025-06-27 09:38:28,149 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/affiliate_views.xml 
2025-06-27 09:38:28,174 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/warehouse_mapping_views.xml 
2025-06-27 09:38:28,197 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/views/menu_views.xml 
2025-06-27 09:38:28,271 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/oauth_templates.xml 
2025-06-27 09:38:28,290 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/templates/dashboard_templates.xml 
2025-06-27 09:38:28,299 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/shop_sync_wizard_views.xml 
2025-06-27 09:38:28,311 8184 INFO ecomplus odoo.modules.loading: loading multichannel_ecommerce/wizards/product_sync_wizard_views.xml 
2025-06-27 09:38:28,461 8184 INFO ecomplus odoo.addons.base.models.ir_module: module multichannel_ecommerce: no translation for language vi_VN 
2025-06-27 09:38:28,526 8184 WARNING ecomplus odoo.modules.loading: The models ['multichannel.shop.sync.wizard', 'multichannel.product.sync.wizard'] have no access rules in module multichannel_ecommerce, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
multichannel_ecommerce.access_multichannel_shop_sync_wizard,access_multichannel_shop_sync_wizard,multichannel_ecommerce.model_multichannel_shop_sync_wizard,base.group_user,1,0,0,0
multichannel_ecommerce.access_multichannel_product_sync_wizard,access_multichannel_product_sync_wizard,multichannel_ecommerce.model_multichannel_product_sync_wizard,base.group_user,1,0,0,0 
2025-06-27 09:38:28,534 8184 INFO ecomplus odoo.modules.loading: Module multichannel_ecommerce loaded in 1.32s, 628 queries (+628 other) 
2025-06-27 09:38:28,552 8184 INFO ecomplus odoo.modules.loading: 176 modules loaded in 1.44s, 628 queries (+628 extra) 
2025-06-27 09:38:30,809 8184 INFO ecomplus odoo.modules.loading: Modules loaded. 
2025-06-27 09:38:30,845 8184 INFO ecomplus odoo.modules.registry: Registry changed, signaling through the database 
2025-06-27 09:38:30,846 8184 INFO ecomplus odoo.modules.registry: Registry loaded in 4.866s 
2025-06-27 09:38:30,847 8184 INFO ecomplus odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-06-27 09:38:30,854 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:30] "POST /web/dataset/call_button/ir.module.module/button_immediate_upgrade HTTP/1.1" 200 - 6418 3.690 2.918
2025-06-27 09:38:31,172 8184 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:38:32,509 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:32] "GET /odoo HTTP/1.1" 200 - 128 0.065 1.275
2025-06-27 09:38:32,918 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:32] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 11 0.039 0.059
2025-06-27 09:38:33,212 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "POST /web/action/load HTTP/1.1" 200 - 7 0.005 0.007
2025-06-27 09:38:33,231 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.007 0.007
2025-06-27 09:38:33,273 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.019 0.018
2025-06-27 09:38:33,287 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 20 0.031 0.023
2025-06-27 09:38:33,321 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "POST /mail/data HTTP/1.1" 200 - 48 0.070 0.035
2025-06-27 09:38:33,715 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:33] "POST /mail/data HTTP/1.1" 200 - 85 0.068 0.054
2025-06-27 09:38:34,135 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:34] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.003 0.009
2025-06-27 09:38:34,546 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:34] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 581 0.315 1.694
2025-06-27 09:38:46,745 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:46] "GET /odoo/discuss HTTP/1.1" 200 - 72 0.027 1.115
2025-06-27 09:38:46,795 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:46] "GET /web/assets/2fe85b7/web.assets_web.min.css HTTP/1.1" 200 - 3 0.003 0.010
2025-06-27 09:38:47,098 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/assets/029520b/web.assets_web.min.js HTTP/1.1" 200 - 3 0.001 0.008
2025-06-27 09:38:47,159 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 1 0.002 0.047
2025-06-27 09:38:47,162 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/assets/f58a84d/web.assets_web_print.min.css HTTP/1.1" 200 - 3 0.040 0.011
2025-06-27 09:38:47,505 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/webclient/load_menus/6b356cde2f1725fc00b92fa47e6fd849ebccf3aea1bf3ee761789451441ec63e HTTP/1.1" 200 - 319 0.222 0.174
2025-06-27 09:38:47,948 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/webclient/translations/ea09801fb028056033ed26957cd5a32d5ab1b16d?lang=vi_VN HTTP/1.1" 200 - 1 0.002 0.061
2025-06-27 09:38:47,978 8184 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:47] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.078
2025-06-27 09:38:48,036 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.054 0.037
2025-06-27 09:38:48,453 8184 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.058
2025-06-27 09:38:48,454 8184 INFO ? werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 200 - 0 0.000 0.061
2025-06-27 09:38:48,484 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "POST /web/action/load HTTP/1.1" 200 - 7 0.019 0.058
2025-06-27 09:38:48,485 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.026 0.036
2025-06-27 09:38:48,574 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "POST /mail/data HTTP/1.1" 200 - 46 0.123 0.042
2025-06-27 09:38:48,663 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 209 0.172 1.713
2025-06-27 09:38:48,839 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 20 0.020 0.016
2025-06-27 09:38:48,871 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.010 0.011
2025-06-27 09:38:48,886 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:48] "POST /mail/data HTTP/1.1" 200 - 82 0.060 0.054
2025-06-27 09:38:49,281 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:49] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.002 0.007
2025-06-27 09:38:57,174 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:57] "GET /odoo/discuss?debug=1 HTTP/1.1" 200 - 72 0.030 0.891
2025-06-27 09:38:57,860 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:57] "POST /web/action/load HTTP/1.1" 200 - 7 0.007 0.009
2025-06-27 09:38:58,060 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 10 0.020 0.038
2025-06-27 09:38:58,074 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.022 0.077
2025-06-27 09:38:58,126 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "POST /mail/data HTTP/1.1" 200 - 46 0.091 0.062
2025-06-27 09:38:58,216 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 20 0.028 0.019
2025-06-27 09:38:58,255 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "POST /mail/data HTTP/1.1" 200 - 82 0.160 0.106
2025-06-27 09:38:58,428 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "POST /mail/inbox/messages HTTP/1.1" 200 - 5 0.033 0.024
2025-06-27 09:38:58,679 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 11 0.011 0.012
2025-06-27 09:38:58,747 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:38:58] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 209 0.176 1.375
2025-06-27 09:39:01,132 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:01] "POST /web/dataset/call_kw/ir.ui.view/has_access HTTP/1.1" 200 - 1 0.000 0.006
2025-06-27 09:39:01,134 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:01] "POST /web/dataset/call_kw/ir.rule/has_access HTTP/1.1" 200 - 1 0.001 0.006
2025-06-27 09:39:01,136 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:01] "POST /web/dataset/call_kw/ir.model.access/has_access HTTP/1.1" 200 - 1 0.000 0.008
2025-06-27 09:39:03,425 8184 INFO ecomplus odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-06-27 09:39:03,426 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:03] "GET /web/become HTTP/1.1" 303 - 7 0.004 0.009
2025-06-27 09:39:04,812 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:04] "GET /odoo HTTP/1.1" 200 - 113 0.050 1.039
2025-06-27 09:39:05,207 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "POST /mail/data HTTP/1.1" 200 - 21 0.016 0.014
2025-06-27 09:39:05,361 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 8 0.004 0.006
2025-06-27 09:39:05,394 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "GET /web/webclient/load_menus/5bad5cbca87f0678b422b32c4000a83d32ae0078d69ee09bc32d08faa095fbe9 HTTP/1.1" 200 - 328 0.121 0.128
2025-06-27 09:39:05,407 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 1 0.002 0.007
2025-06-27 09:39:05,418 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.002 0.006
2025-06-27 09:39:05,785 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.003 0.008
2025-06-27 09:39:05,797 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.008 0.014
2025-06-27 09:39:05,799 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "POST /web/action/load HTTP/1.1" 200 - 6 0.016 0.010
2025-06-27 09:39:05,823 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 7 0.010 0.038
2025-06-27 09:39:05,829 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:05] "GET /web/image?model=res.users&field=avatar_128&id=1 HTTP/1.1" 200 - 7 0.008 0.026
2025-06-27 09:39:06,160 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:06] "POST /mail/data HTTP/1.1" 200 - 37 0.022 0.024
2025-06-27 09:39:06,198 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:06] "GET /web/image/res.partner/2/avatar_128?unique=1750972583000 HTTP/1.1" 200 - 6 0.005 0.013
2025-06-27 09:39:06,605 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:06] "POST /mail/inbox/messages HTTP/1.1" 200 - 3 0.012 0.063
2025-06-27 09:39:07,115 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:07] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 570 0.278 1.440
2025-06-27 09:39:07,995 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:07] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.006
2025-06-27 09:39:08,273 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:08] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 23 0.009 0.017
2025-06-27 09:39:08,313 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:08] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.002 0.002
2025-06-27 09:39:12,107 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "GET /odoo/action-883 HTTP/1.1" 200 - 65 0.026 1.125
2025-06-27 09:39:12,782 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "POST /web/action/load HTTP/1.1" 200 - 10 0.007 0.010
2025-06-27 09:39:12,899 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "POST /mail/data HTTP/1.1" 200 - 20 0.009 0.017
2025-06-27 09:39:12,907 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.007 0.012
2025-06-27 09:39:12,919 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.005 0.012
2025-06-27 09:39:12,931 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:12] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 11 0.011 0.030
2025-06-27 09:39:13,096 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:13] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.002 0.004
2025-06-27 09:39:13,292 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:13] "GET /web/image?model=res.users&field=avatar_128&id=1 HTTP/1.1" 304 - 7 0.022 0.044
2025-06-27 09:39:13,297 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:13] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.020 0.014
2025-06-27 09:39:13,893 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:13] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 198 0.142 1.589
2025-06-27 09:39:25,160 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "GET /odoo/action-883?debug=2 HTTP/1.1" 200 - 65 0.024 0.877
2025-06-27 09:39:25,514 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.011 0.075
2025-06-27 09:39:25,865 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "POST /web/action/load HTTP/1.1" 200 - 10 0.009 0.017
2025-06-27 09:39:25,902 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "POST /mail/data HTTP/1.1" 200 - 20 0.033 0.029
2025-06-27 09:39:25,910 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "GET /web/image?model=res.users&field=avatar_128&id=1 HTTP/1.1" 304 - 7 0.018 0.023
2025-06-27 09:39:25,922 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "GET /web/image?model=res.company&field=appbar_image&id=1 HTTP/1.1" 304 - 5 0.018 0.030
2025-06-27 09:39:25,922 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:25] "POST /web/dataset/call_kw/mail.push.device/register_devices HTTP/1.1" 200 - 6 0.010 0.055
2025-06-27 09:39:26,216 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:26] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 11 0.012 0.020
2025-06-27 09:39:26,530 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:26] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 198 0.096 1.252
2025-06-27 09:39:26,537 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:26] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.001 0.004
2025-06-27 09:39:31,036 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:31] "POST /web/dataset/call_kw/ir.ui.view/has_access HTTP/1.1" 200 - 1 0.000 0.006
2025-06-27 09:39:31,039 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:31] "POST /web/dataset/call_kw/ir.model.access/has_access HTTP/1.1" 200 - 1 0.001 0.007
2025-06-27 09:39:31,040 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:31] "POST /web/dataset/call_kw/ir.rule/has_access HTTP/1.1" 200 - 1 0.001 0.008
2025-06-27 09:39:38,781 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:38] "POST /web/dataset/call_kw/ir.model/search HTTP/1.1" 200 - 2 0.000 0.004
2025-06-27 09:39:39,146 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:39] "POST /web/dataset/call_kw/ir.model.access/get_views HTTP/1.1" 200 - 24 0.007 0.036
2025-06-27 09:39:39,360 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:39] "POST /web/dataset/call_kw/ir.model.access/web_search_read HTTP/1.1" 200 - 5 0.004 0.002
2025-06-27 09:39:39,478 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:39] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 1 0.001 0.003
2025-06-27 09:39:43,433 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) starting 
2025-06-27 09:39:43,476 8184 INFO ecomplus odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-06-27 09:39:43,528 8184 INFO ecomplus odoo.addons.base.models.res_users: GC'd 1 user log entries 
2025-06-27 09:39:43,538 8184 INFO ecomplus odoo.addons.base.models.res_users: GC 'res.users.apikeys' delete 0 entries 
2025-06-27 09:39:43,542 8184 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 1 entries 
2025-06-27 09:39:43,545 8184 INFO ecomplus odoo.addons.base.models.res_device: GC device logs delete 0 entries 
2025-06-27 09:39:43,558 8184 INFO ecomplus odoo.addons.base.models.res_users: GC 'auth_totp.device' delete 0 entries 
2025-06-27 09:39:43,566 8184 INFO ecomplus odoo.models.unlink: User #1 deleted bus.bus records with IDs: [1618, 1619, 1620, 1621] 
2025-06-27 09:39:43,674 8184 INFO ecomplus odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-06-27 09:39:43,956 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) done in 0.523s 
2025-06-27 09:39:43,959 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) processed 0 records, 0 records remaining 
2025-06-27 09:39:43,963 8184 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Base: Auto-vacuum internal data' (1) completed 
2025-06-27 09:39:48,590 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:48] "POST /web/action/load HTTP/1.1" 200 - 9 0.003 0.008
2025-06-27 09:39:50,551 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:50] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 470 0.139 1.493
2025-06-27 09:39:50,850 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:50] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 299 0.104 0.154
2025-06-27 09:39:51,399 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:51] "POST /base_setup/demo_active HTTP/1.1" 200 - 2 0.002 0.003
2025-06-27 09:39:51,416 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:51] "POST /base_setup/data HTTP/1.1" 200 - 6 0.002 0.004
2025-06-27 09:39:55,330 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:55] "POST /web/action/load HTTP/1.1" 200 - 12 0.003 0.009
2025-06-27 09:39:55,985 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:55] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 166 0.103 0.219
2025-06-27 09:39:56,006 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:56] "POST /web/dataset/call_kw/res.users/web_search_read HTTP/1.1" 200 - 6 0.004 0.009
2025-06-27 09:39:58,201 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:58] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 51 0.026 0.024
2025-06-27 09:39:58,429 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:39:58] "GET /web/image/res.users/2/avatar_128?unique=1751017198272 HTTP/1.1" 200 - 7 0.004 0.007
2025-06-27 09:40:39,735 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:39] "POST /web/dataset/call_button/res.users/action_karma_report HTTP/1.1" 200 - 1 0.001 0.028
2025-06-27 09:40:40,184 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:40] "POST /web/dataset/call_kw/gamification.karma.tracking/get_views HTTP/1.1" 200 - 21 0.009 0.110
2025-06-27 09:40:40,319 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:40] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 3 0.002 0.006
2025-06-27 09:40:40,506 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:40] "POST /web/dataset/call_kw/gamification.karma.tracking/web_search_read HTTP/1.1" 200 - 6 0.004 0.006
2025-06-27 09:40:40,645 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:40] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 7 0.005 0.008
2025-06-27 09:40:45,450 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:45] "POST /web/action/load HTTP/1.1" 200 - 13 0.004 0.011
2025-06-27 09:40:45,819 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:45] "POST /web/dataset/call_kw/res.users/web_read HTTP/1.1" 200 - 50 0.019 0.033
2025-06-27 09:40:46,043 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:46] "GET /web/image/res.users/2/avatar_128?unique=1751017245830 HTTP/1.1" 200 - 7 0.004 0.006
2025-06-27 09:40:49,647 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:49] "POST /web/dataset/call_button/res.users/action_show_rules HTTP/1.1" 200 - 4 0.003 0.007
2025-06-27 09:40:50,010 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:50] "POST /web/dataset/call_kw/ir.rule/get_views HTTP/1.1" 200 - 30 0.013 0.034
2025-06-27 09:40:50,242 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:40:50] "POST /web/dataset/call_kw/ir.rule/web_search_read HTTP/1.1" 200 - 7 0.003 0.010
2025-06-27 09:41:07,495 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:07] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 101 0.029 0.105
2025-06-27 09:41:11,325 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:11] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.002 0.002
2025-06-27 09:41:12,789 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:12] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 61 0.022 0.043
2025-06-27 09:41:13,114 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:13] "POST /web/dataset/call_kw/multichannel.shop/onchange HTTP/1.1" 200 - 4 0.002 0.006
2025-06-27 09:41:28,118 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:28] "POST /web/dataset/call_kw/multichannel.shop/web_save HTTP/1.1" 200 - 11 0.012 0.007
2025-06-27 09:41:45,220 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:45] "POST /web/dataset/call_button/multichannel.shop/action_connect_platform HTTP/1.1" 200 - 4 0.000 0.005
2025-06-27 09:41:45,477 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:41:45] "POST /web/dataset/call_kw/multichannel.shop/web_read HTTP/1.1" 200 - 8 0.004 0.005
2025-06-27 09:42:11,676 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:11] "GET /tiktok/callback?app_key=6gma7e0k0ji9a&code=ROW_lAjXLwAAAABfwlsrdgB1YNoExkdSKZ_4toR0z7-pebeQmxBeT-_M9cSy7HNAsFM5VI7LZW1pklv5a2R1jKcSXDoO80H50XyVRpflZn01SJHJnoW9f6wP9g&locale=vi-VN&shop_region=VN&state=1 HTTP/1.1" 404 - 198 0.091 1.176
2025-06-27 09:42:14,123 8184 WARNING ecomplus odoo.addons.base.models.assetsbundle: Could not execute command 'sassc'This error occurred while compiling the bundle 'web.assets_frontend' containing:
    - /web/static/lib/bootstrap/scss/_functions.scss
    - /web/static/lib/bootstrap/scss/_mixins.scss
    - /web/static/src/scss/functions.scss
    - /web/static/src/scss/mixins_forwardport.scss
    - /web/static/src/scss/bs_mixins_overrides.scss
    - /web/static/src/scss/utils.scss
    - /web/static/src/scss/primary_variables.scss
    - /muk_web_chatter/static/src/scss/variables.scss
    - /web/static/src/core/avatar/avatar.variables.scss
    - /web/static/src/core/notifications/notification.variables.scss
    - /web/static/src/search/control_panel/control_panel.variables.scss
    - /web/static/src/search/search_bar/search_bar.variables.scss
    - /web/static/src/search/search_panel/search_panel.variables.scss
    - /web/static/src/views/fields/statusbar/statusbar_field.variables.scss
    - /web/static/src/views/fields/translation_button.variables.scss
    - /web/static/src/views/form/form.variables.scss
    - /web/static/src/views/kanban/kanban.variables.scss
    - /web/static/src/webclient/burger_menu/burger_menu.variables.scss
    - /web/static/src/webclient/navbar/navbar.variables.scss
    - /mail/static/src/core/common/primary_variables.scss
    - /mail/static/src/discuss/typing/common/primary_variables.scss
    - /mail/static/src/scss/variables/primary_variables.scss
    - /onboarding/static/src/scss/onboarding.variables.scss
    - /web_editor/static/src/scss/web_editor.variables.scss
    - /web_editor/static/src/scss/wysiwyg.variables.scss
    - /portal/static/src/scss/primary_variables.scss
    - /account/static/src/scss/variables.scss
    - /website/static/src/scss/primary_variables.scss
    - /_custom/web.assets_frontend/website/static/src/scss/options/user_values.scss
    - /website/static/src/scss/options/colors/user_color_palette.scss
    - /website/static/src/scss/options/colors/user_gray_color_palette.scss
    - /_custom/web.assets_frontend/website/static/src/scss/options/colors/user_theme_color_palette.scss
    - /website_sale/static/src/scss/primary_variables.scss
    - /hr_org_chart/static/src/scss/variables.scss
    - /muk_web_appsbar/static/src/scss/variables.scss
    - /website/static/src/snippets/s_badge/000_variables.scss
    - /website/static/src/snippets/s_product_list/000_variables.scss
    - /theme_cobalt/static/src/scss/primary_variables.scss
    - /website/static/src/scss/secondary_variables.scss
    - /web/static/src/scss/secondary_variables.scss
    - /web_editor/static/src/scss/secondary_variables.scss
    - /website/static/src/scss/user_custom_bootstrap_overridden.scss
    - /theme_cobalt/static/src/scss/bootstrap_overridden.scss
    - /website/static/src/scss/bootstrap_overridden.scss
    - /portal/static/src/scss/bootstrap_overridden.scss
    - /web_editor/static/src/scss/bootstrap_overridden.scss
    - /web/static/src/scss/bootstrap_overridden_frontend.scss
    - /web/static/src/scss/pre_variables.scss
    - /web/static/lib/bootstrap/scss/_variables.scss
    - /web/static/lib/bootstrap/scss/_variables-dark.scss
    - /web/static/lib/bootstrap/scss/_maps.scss
    - /web/static/src/scss/import_bootstrap.scss
    - /web/static/src/scss/utilities_custom.scss
    - /web/static/lib/bootstrap/scss/utilities/_api.scss
    - /web/static/src/scss/bootstrap_review.scss
    - /web/static/src/scss/bootstrap_review_frontend.scss
    - /web/static/src/webclient/navbar/navbar.scss
    - /web/static/src/scss/animation.scss
    - /web/static/src/scss/base_frontend.scss
    - /web/static/src/scss/fontawesome_overridden.scss
    - /web/static/src/scss/mimetypes.scss
    - /web/static/src/scss/ui.scss
    - /web/static/src/views/fields/translation_dialog.scss
    - /web/static/src/views/fields/signature/signature_field.scss
    - /web/static/src/legacy/scss/ui.scss
    - /web/static/src/core/utils/transitions.scss
    - /web/static/src/core/action_swiper/action_swiper.scss
    - /web/static/src/core/autocomplete/autocomplete.scss
    - /web/static/src/core/avatar/avatar.scss
    - /web/static/src/core/barcode/barcode_dialog.scss
    - /web/static/src/core/barcode/crop_overlay.scss
    - /web/static/src/core/checkbox/checkbox.scss
    - /web/static/src/core/colorlist/colorlist.scss
    - /web/static/src/core/colorpicker/colorpicker.scss
    - /web/static/src/core/datetime/datetime_picker.scss
    - /web/static/src/core/debug/debug_menu.scss
    - /web/static/src/core/dialog/dialog.scss
    - /web/static/src/core/dropdown/accordion_item.scss
    - /web/static/src/core/dropdown/dropdown.scss
    - /web/static/src/core/dropzone/dropzone.scss
    - /web/static/src/core/effects/rainbow_man.scss
    - /web/static/src/core/emoji_picker/emoji_picker.dark.scss
    - /web/static/src/core/emoji_picker/emoji_picker.scss
    - /web/static/src/core/errors/error_dialog.scss
    - /web/static/src/core/file_upload/file_upload_progress_bar.scss
    - /web/static/src/core/file_upload/file_upload_progress_record.scss
    - /web/static/src/core/file_viewer/file_viewer.scss
    - /web/static/src/core/model_field_selector/model_field_selector.scss
    - /web/static/src/core/model_field_selector/model_field_selector_popover.scss
    - /web/static/src/core/model_selector/model_selector.scss
    - /web/static/src/core/notebook/notebook.scss
    - /web/static/src/core/notifications/notification.scss
    - /web/static/src/core/overlay/overlay_container.scss
    - /web/static/src/core/pager/pager_indicator.scss
    - /web/static/src/core/popover/popover.scss
    - /web/static/src/core/pwa/install_prompt.scss
    - /web/static/src/core/record_selectors/record_selectors.scss
    - /web/static/src/core/resizable_panel/resizable_panel.scss
    - /web/static/src/core/select_menu/select_menu.scss
    - /web/static/src/core/signature/name_and_signature.scss
    - /web/static/src/core/tags_list/tags_list.scss
    - /web/static/src/core/tooltip/tooltip.scss
    - /web/static/src/core/tree_editor/tree_editor.scss
    - /web/static/src/core/ui/block_ui.scss
    - /web/static/src/core/utils/draggable_hook_builder.scss
    - /web/static/src/core/utils/nested_sortable.scss
    - /web_tour/static/src/tour_pointer/tour_pointer.scss
    - /html_editor/static/src/main/media/media_dialog/upload_progress_toast/upload_progress_toast.scss
    - /web_editor/static/src/components/media_dialog/media_dialog.scss
    - /web_editor/static/src/components/upload_progress_toast/upload_progress_toast.scss
    - /web_editor/static/src/js/editor/odoo-editor/src/base_style.scss
    - /web_editor/static/src/scss/web_editor.common.scss
    - /web_editor/static/src/scss/web_editor.frontend.scss
    - /portal/static/src/scss/portal.scss
    - /payment/static/src/scss/payment_form.scss
    - /payment/static/src/scss/payment_provider.scss
    - /payment/static/src/scss/portal_templates.scss
    - /sale/static/src/scss/sale_portal.scss
    - /stock/static/src/scss/stock_traceability_report.scss
    - /google_recaptcha/static/src/scss/recaptcha.scss
    - /website/static/src/libs/zoomodoo/zoomodoo.scss
    - /website/static/src/scss/website.scss
    - /website/static/src/scss/website_controller_page.scss
    - /website/static/src/scss/website.ui.scss
    - /website/static/src/components/autocomplete_with_pages/url_autocomplete.scss
    - /rating/static/src/scss/rating_templates.scss
    - /project/static/src/scss/portal_rating.scss
    - /project/static/src/scss/project_sharing_frontend.scss
    - /website_mail/static/src/css/website_mail.scss
    - /portal_rating/static/src/scss/portal_rating.scss
    - /website_sale/static/src/scss/website_sale.scss
    - /website_sale/static/src/scss/website_sale_frontend.scss
    - /website_sale/static/src/scss/website_sale_delivery.scss
    - /website/static/lib/multirange/multirange_custom.scss
    - /website_sale/static/src/scss/product_configurator.scss
    - /sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.scss
    - /sale/static/src/js/product/product.scss
    - /sale/static/src/js/product_card/product_card.scss
    - /sale/static/src/js/product_template_attribute_line/product_template_attribute_line.scss
    - /sale/static/src/js/quantity_buttons/quantity_buttons.scss
    - /website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.scss
    - /website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.scss
    - /delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.scss
    - /delivery/static/src/js/location_selector/map/map.scss
    - /website_profile/static/src/scss/website_profile.scss
    - /website_slides/static/src/scss/website_slides.scss
    - /website_slides/static/src/scss/website_slides_profile.scss
    - /website_slides/static/src/scss/slides_slide_fullscreen.scss
    - /website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_xp_progress_bar.scss
    - /website_event/static/src/scss/event_templates_common.scss
    - /website_event/static/src/scss/event_templates_list.scss
    - /website_event/static/src/scss/event_templates_page.scss
    - /mail/static/src/core/common/attachment_list.scss
    - /mail/static/src/core/common/attachment_view.scss
    - /mail/static/src/core/common/autoresize_input.scss
    - /mail/static/src/core/common/chat_bubble.scss
    - /mail/static/src/core/common/chat_hub.scss
    - /mail/static/src/core/common/chat_window.scss
    - /mail/static/src/core/common/composer.scss
    - /mail/static/src/core/common/core.scss
    - /mail/static/src/core/common/country_flag.scss
    - /mail/static/src/core/common/im_status.scss
    - /mail/static/src/core/common/link_preview.scss
    - /mail/static/src/core/common/message.scss
    - /mail/static/src/core/common/message_action_menu_mobile.scss
    - /mail/static/src/core/common/message_card_list.scss
    - /mail/static/src/core/common/message_in_reply.scss
    - /mail/static/src/core/common/message_reaction_list.scss
    - /mail/static/src/core/common/message_reaction_menu.scss
    - /mail/static/src/core/common/message_reactions.scss
    - /mail/static/src/core/common/message_seen_indicator.scss
    - /mail/static/src/core/common/navigable_list.scss
    - /mail/static/src/core/common/picker.scss
    - /mail/static/src/core/common/picker_content.scss
    - /mail/static/src/core/common/thread.scss
    - /mail/static/src/discuss/core/common/action_panel.scss
    - /mail/static/src/discuss/core/common/channel_invitation.scss
    - /mail/static/src/discuss/core/common/channel_member_list.scss
    - /mail/static/src/discuss/core/common/discuss_notification_settings.scss
    - /mail/static/src/discuss/core/common/notification_settings.scss
    - /mail/static/src/discuss/call/common/call.scss
    - /mail/static/src/discuss/call/common/call_action_list.scss
    - /mail/static/src/discuss/call/common/call_invitation.scss
    - /mail/static/src/discuss/call/common/call_invitations.scss
    - /mail/static/src/discuss/call/common/call_menu.scss
    - /mail/static/src/discuss/call/common/call_participant_card.scss
    - /mail/static/src/discuss/call/common/chat_window_patch.scss
    - /mail/static/src/discuss/call/common/discuss_patch.scss
    - /mail/static/src/discuss/typing/common/typing.scss
    - /im_livechat/static/src/embed/common/chat_window_patch.scss
    - /im_livechat/static/src/embed/common/close_confirmation.scss
    - /im_livechat/static/src/embed/common/emoji_picker.scss
    - /im_livechat/static/src/embed/common/livechat_button.scss
    - /im_livechat/static/src/embed/common/scss/bootstrap_overridden.scss
    - /im_livechat/static/src/embed/common/scss/shadow.scss
    - /im_livechat/static/src/embed/common/thread_patch.scss
    - /auth_totp_portal/static/src/scss/auth_totp_portal.scss
    - /website_livechat/static/src/patch/assets_frontend/website.scss
    - /website_event_sale/static/src/scss/website_event_sale_templates.scss
    - /website_mass_mailing/static/src/scss/website_mass_mailing_popup.scss
    - /website_sale_stock/static/src/js/product_card/product_card.scss
    - /website_slides_survey/static/src/scss/website_slides_survey.scss
    - /website_blog/static/src/scss/website_blog.scss
    - /mass_mailing/static/src/snippets/s_alert/000.scss
    - /mass_mailing/static/src/snippets/s_features_grid/000.scss
    - /mass_mailing/static/src/snippets/s_hr/000.scss
    - /mass_mailing/static/src/snippets/s_masonry_block/001.scss
    - /mass_mailing/static/src/snippets/s_media_list/001.scss
    - /mass_mailing/static/src/snippets/s_rating/001.scss
    - /website/static/src/snippets/s_title/000.scss
    - /website/static/src/snippets/s_text_cover/000.scss
    - /website/static/src/snippets/s_instagram_page/000.scss
    - /website/static/src/snippets/s_features/000.scss
    - /website/static/src/snippets/s_carousel/001.scss
    - /website/static/src/snippets/s_carousel_intro/000.scss
    - /website/static/src/snippets/s_alert/000.scss
    - /website/static/src/snippets/s_card/000.scss
    - /website/static/src/snippets/s_share/000.scss
    - /website/static/src/snippets/s_social_media/000.scss
    - /website/static/src/snippets/s_rating/001.scss
    - /website/static/src/snippets/s_hr/000.scss
    - /website/static/src/snippets/s_image_gallery/002.scss
    - /website/static/src/snippets/s_company_team/000.scss
    - /website/static/src/snippets/s_references/000.scss
    - /website/static/src/snippets/s_popup/001.scss
    - /website/static/src/snippets/s_features_grid/000.scss
    - /website/static/src/snippets/s_features_grid/001.scss
    - /website/static/src/snippets/s_tabs/002.scss
    - /website/static/src/snippets/s_table_of_content/000.scss
    - /website/static/src/snippets/s_quotes_carousel/002.scss
    - /website/static/src/snippets/s_masonry_block/001.scss
    - /website/static/src/snippets/s_media_list/001.scss
    - /website/static/src/snippets/s_showcase/003.scss
    - /website/static/src/snippets/s_timeline/002.scss
    - /website/static/src/snippets/s_timeline_list/000.scss
    - /website/static/src/snippets/s_process_steps/002.scss
    - /website/static/src/snippets/s_accordion/000.scss
    - /website/static/src/snippets/s_text_highlight/000.scss
    - /website/static/src/snippets/s_pricelist_cafe/000.scss
    - /website/static/src/snippets/s_progress_bar/001.scss
    - /website/static/src/snippets/s_blockquote/001.scss
    - /website/static/src/snippets/s_badge/000.scss
    - /website/static/src/snippets/s_color_blocks_2/000.scss
    - /website/static/src/snippets/s_product_list/000.scss
    - /website/static/src/snippets/s_mega_menu_thumbnails/000.scss
    - /website/static/src/snippets/s_mega_menu_little_icons/000.scss
    - /website/static/src/snippets/s_mega_menu_images_subtitles/000.scss
    - /website/static/src/snippets/s_mega_menu_menus_logos/000.scss
    - /website/static/src/snippets/s_mega_menu_odoo_menu/000.scss
    - /website/static/src/snippets/s_mega_menu_big_icons_subtitles/000.scss
    - /website/static/src/snippets/s_mega_menu_cards/000.scss
    - /website/static/src/snippets/s_google_map/000.scss
    - /website/static/src/snippets/s_map/000.scss
    - /website/static/src/snippets/s_dynamic_snippet/000.scss
    - /website/static/src/snippets/s_dynamic_snippet_carousel/000.scss
    - /website/static/src/snippets/s_embed_code/000.scss
    - /website/static/src/snippets/s_website_form/001.scss
    - /website/static/src/snippets/s_quadrant/000.scss
    - /website_payment/static/src/snippets/s_donation/000.scss
    - /website/static/src/scss/options/ripple_effect.scss
    - /website_blog/static/src/snippets/s_blog_posts/000.scss
    - /website_event/static/src/snippets/s_events/000.scss
    - /website_sale/static/src/snippets/s_dynamic_snippet_products/000.scss
    - /website/static/src/scss/user_custom_rules.scss 
2025-06-27 09:42:14,265 8184 INFO ecomplus odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/74b47e3/web.assets_frontend.min.css (id:1569) 
2025-06-27 09:42:15,460 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:15] "GET /web_editor/shape/http_routing/404.svg?c2=o-color-2 HTTP/1.1" 400 - 224 0.111 3.551
2025-06-27 09:42:39,522 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:39] "POST /web/dataset/call_button/multichannel.shop/action_connect_platform HTTP/1.1" 200 - 4 0.001 0.029
2025-06-27 09:42:39,764 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:39] "POST /web/dataset/call_kw/multichannel.shop/web_read HTTP/1.1" 200 - 8 0.005 0.003
2025-06-27 09:42:50,605 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:50] "POST /web/action/load HTTP/1.1" 200 - 9 0.005 0.005
2025-06-27 09:42:51,022 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:51] "POST /web/dataset/call_kw/multichannel.warehouse.mapping/get_views HTTP/1.1" 200 - 20 0.005 0.016
2025-06-27 09:42:51,257 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:51] "POST /web/dataset/call_kw/multichannel.warehouse.mapping/web_search_read HTTP/1.1" 200 - 2 0.002 0.004
2025-06-27 09:42:54,773 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:54] "POST /web/action/load HTTP/1.1" 200 - 9 0.003 0.008
2025-06-27 09:42:55,114 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:55] "POST /web/dataset/call_kw/multichannel.product.mapping/get_views HTTP/1.1" 200 - 20 0.005 0.019
2025-06-27 09:42:55,359 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:55] "POST /web/dataset/call_kw/multichannel.product.mapping/web_search_read HTTP/1.1" 200 - 2 0.001 0.003
2025-06-27 09:42:58,529 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:58] "POST /web/action/load HTTP/1.1" 200 - 9 0.004 0.006
2025-06-27 09:42:58,871 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:58] "POST /web/dataset/call_kw/multichannel.transaction/get_views HTTP/1.1" 200 - 22 0.010 0.015
2025-06-27 09:42:59,118 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:42:59] "POST /web/dataset/call_kw/multichannel.transaction/web_search_read HTTP/1.1" 200 - 2 0.001 0.003
2025-06-27 09:43:05,886 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:05] "POST /web/action/load HTTP/1.1" 200 - 9 0.006 0.005
2025-06-27 09:43:06,239 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:06] "POST /web/dataset/call_kw/multichannel.shop/get_views HTTP/1.1" 200 - 22 0.010 0.028
2025-06-27 09:43:06,470 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:06] "POST /web/dataset/call_kw/multichannel.shop/web_search_read HTTP/1.1" 200 - 2 0.002 0.002
2025-06-27 09:43:13,334 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:13] "POST /web/action/load HTTP/1.1" 200 - 10 0.005 0.010
2025-06-27 09:43:13,838 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:13] "POST /web/dataset/call_kw/pos.config/get_views HTTP/1.1" 200 - 40 0.059 0.115
2025-06-27 09:43:13,955 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:13] "POST /web/dataset/call_kw/pos.config/web_search_read HTTP/1.1" 200 - 22 0.008 0.020
2025-06-27 09:43:14,170 8184 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:14] "POST /web/dataset/call_kw/pos.config/get_pos_kanban_view_state HTTP/1.1" 200 - 6 0.002 0.006
2025-06-27 09:43:21,133 15248 INFO ? odoo.modules.loading: loading 1 modules... 
2025-06-27 09:43:21,140 15248 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-06-27 09:43:21,246 15248 INFO ? odoo.modules.loading: loading 176 modules... 
2025-06-27 09:43:23,063 15248 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.transaction is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 5, in <module>
    from . import transaction
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 6, in <module>
    class MultichannelTransaction(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\transaction.py", line 201, in MultichannelTransaction
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:43:23,068 15248 WARNING ? py.warnings: C:\Program Files\Odoo 18.0.********\server\odoo\api.py:482: DeprecationWarning: The model odoo.addons.multichannel_ecommerce.models.affiliate is not overriding the create method in batch
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
    super().handle()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\http\server.py", line 424, in handle_one_request
    method()
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 362, in run_wsgi
    execute(self.server.app)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\werkzeug\serving.py", line 323, in execute
    application_iter = app(environ, start_response)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 2390, in __call__
    response = request._serve_db()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1894, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "C:\Program Files\Odoo 18.0.********\server\odoo\http.py", line 1504, in _open_registry
    registry = Registry(self.db)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 106, in __new__
    return cls.new(db_name)
  File "C:\Program Files\Odoo 18.0.********\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\tools\func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\modules\module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\__init__.py", line 3, in <module>
    from . import models
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\__init__.py", line 7, in <module>
    from . import affiliate
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 5, in <module>
    class MultichannelAffiliate(models.Model):
  File "C:\Program Files\Odoo 18.0.********\server\odoo\addons\multichannel_ecommerce\models\affiliate.py", line 220, in MultichannelAffiliate
    @api.model
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 429, in model
    return model_create_single(method)
  File "C:\Program Files\Odoo 18.0.********\server\odoo\api.py", line 482, in model_create_single
    warnings.warn(
 
2025-06-27 09:43:23,439 15248 INFO ? odoo.modules.loading: 176 modules loaded in 2.19s, 0 queries (+0 extra) 
2025-06-27 09:43:23,664 15248 INFO ? odoo.modules.loading: Modules loaded. 
2025-06-27 09:43:23,671 15248 INFO ? odoo.modules.registry: Registry loaded in 2.586s 
2025-06-27 09:43:23,674 15248 INFO ecomplus odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-06-27 09:43:23,742 15248 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:23] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 24 0.031 2.626
2025-06-27 09:43:23,744 15248 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:23] "GET /websocket?version=18.0-5 HTTP/1.1" 101 - 4 0.013 1.765
2025-06-27 09:43:24,136 15248 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:24] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.004 0.073
2025-06-27 09:43:24,137 15248 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:43:24] "POST /bus/has_missed_notifications HTTP/1.1" 200 - 2 0.004 0.073
2025-06-27 09:43:24,155 15248 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-06-27 09:43:44,186 15248 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) starting 
2025-06-27 09:43:44,195 15248 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) done in 0.008s 
2025-06-27 09:43:44,198 15248 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) processed 0 records, 0 records remaining 
2025-06-27 09:43:44,201 15248 INFO ecomplus odoo.addons.base.models.ir_cron: Job 'Event: Mail Scheduler' (36) completed 
2025-06-27 09:47:21,142 15248 INFO ecomplus werkzeug: 127.0.0.1 - - [27/Jun/2025 09:47:21] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.001 0.045
2025-06-27 09:58:43,581 15248 INFO ? odoo.service.server: Initiating shutdown 
2025-06-27 09:58:43,581 15248 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-06-27 09:58:43,923 15248 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 4 connections  

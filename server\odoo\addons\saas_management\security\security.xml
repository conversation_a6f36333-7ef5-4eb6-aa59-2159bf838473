<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- SaaS Management Categories -->
        <record id="module_category_saas" model="ir.module.category">
            <field name="name">SaaS Management</field>
            <field name="description">Manage SaaS instances, clients, and subscriptions</field>
            <field name="sequence">20</field>
        </record>

        <!-- SaaS Management Groups -->
        <record id="group_saas_user" model="res.groups">
            <field name="name">SaaS User</field>
            <field name="category_id" ref="module_category_saas"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Basic access to view SaaS data</field>
        </record>

        <record id="group_saas_manager" model="res.groups">
            <field name="name">SaaS Manager</field>
            <field name="category_id" ref="module_category_saas"/>
            <field name="implied_ids" eval="[(4, ref('group_saas_user'))]"/>
            <field name="comment">Manage clients, instances, plans, and subscriptions</field>
        </record>

        <record id="group_saas_admin" model="res.groups">
            <field name="name">SaaS Administrator</field>
            <field name="category_id" ref="module_category_saas"/>
            <field name="implied_ids" eval="[(4, ref('group_saas_manager'))]"/>
            <field name="comment">Full access to SaaS management including servers, domains, and system configuration</field>
        </record>

        <!-- Record Rules -->
        
        <!-- SaaS Client Rules -->
        <record id="saas_client_rule_admin" model="ir.rule">
            <field name="name">SaaS Client: Admin Access</field>
            <field name="model_id" ref="model_saas_client"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_client_rule_manager" model="ir.rule">
            <field name="name">SaaS Client: Manager Access</field>
            <field name="model_id" ref="model_saas_client"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="saas_client_rule_portal" model="ir.rule">
            <field name="name">SaaS Client: Portal Access</field>
            <field name="model_id" ref="model_saas_client"/>
            <field name="domain_force">[('partner_id.user_ids', 'in', [user.id])]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- SaaS Instance Rules -->
        <record id="saas_instance_rule_admin" model="ir.rule">
            <field name="name">SaaS Instance: Admin Access</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_instance_rule_manager" model="ir.rule">
            <field name="name">SaaS Instance: Manager Access</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="saas_instance_rule_portal" model="ir.rule">
            <field name="name">SaaS Instance: Portal Access</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="domain_force">[('client_id.partner_id.user_ids', 'in', [user.id])]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- SaaS Plan Rules -->
        <record id="saas_plan_rule_admin" model="ir.rule">
            <field name="name">SaaS Plan: Admin Access</field>
            <field name="model_id" ref="model_saas_plan"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_plan_rule_public" model="ir.rule">
            <field name="name">SaaS Plan: Public Access</field>
            <field name="model_id" ref="model_saas_plan"/>
            <field name="domain_force">[('active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_public'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- SaaS Server Rules -->
        <record id="saas_server_rule_admin" model="ir.rule">
            <field name="name">SaaS Server: Admin Only</field>
            <field name="model_id" ref="model_saas_server"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- SaaS Backup Rules -->
        <record id="saas_backup_rule_admin" model="ir.rule">
            <field name="name">SaaS Backup: Admin Access</field>
            <field name="model_id" ref="model_saas_backup"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_backup_rule_manager" model="ir.rule">
            <field name="name">SaaS Backup: Manager Access</field>
            <field name="model_id" ref="model_saas_backup"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="saas_backup_rule_portal" model="ir.rule">
            <field name="name">SaaS Backup: Portal Access</field>
            <field name="model_id" ref="model_saas_backup"/>
            <field name="domain_force">[('instance_id.client_id.partner_id.user_ids', 'in', [user.id])]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- SaaS Domain Rules -->
        <record id="saas_domain_rule_admin" model="ir.rule">
            <field name="name">SaaS Domain: Admin Only</field>
            <field name="model_id" ref="model_saas_domain"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- SaaS Custom Domain Rules -->
        <record id="saas_custom_domain_rule_admin" model="ir.rule">
            <field name="name">SaaS Custom Domain: Admin Access</field>
            <field name="model_id" ref="model_saas_custom_domain"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_custom_domain_rule_portal" model="ir.rule">
            <field name="name">SaaS Custom Domain: Portal Access</field>
            <field name="model_id" ref="model_saas_custom_domain"/>
            <field name="domain_force">[('instance_id.client_id.partner_id.user_ids', 'in', [user.id])]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- SaaS App Rules -->
        <record id="saas_app_rule_public" model="ir.rule">
            <field name="name">SaaS App: Public Access</field>
            <field name="model_id" ref="model_saas_app"/>
            <field name="domain_force">[('active', '=', True)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_public')), (4, ref('group_saas_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="saas_app_rule_admin" model="ir.rule">
            <field name="name">SaaS App: Admin Access</field>
            <field name="model_id" ref="model_saas_app"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- SaaS Subscription Rules -->
        <record id="saas_subscription_rule_admin" model="ir.rule">
            <field name="name">SaaS Subscription: Admin Access</field>
            <field name="model_id" ref="model_saas_subscription"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_admin'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="saas_subscription_rule_manager" model="ir.rule">
            <field name="name">SaaS Subscription: Manager Access</field>
            <field name="model_id" ref="model_saas_subscription"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_saas_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="saas_subscription_rule_portal" model="ir.rule">
            <field name="name">SaaS Subscription: Portal Access</field>
            <field name="model_id" ref="model_saas_subscription"/>
            <field name="domain_force">[('client_id.partner_id.user_ids', 'in', [user.id])]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

    </data>
</odoo>

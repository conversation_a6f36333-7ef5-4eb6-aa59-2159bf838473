# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
import logging
import subprocess
import json

_logger = logging.getLogger(__name__)


class SaasInstance(models.Model):
    _name = 'saas.instance'
    _description = 'SaaS Instance'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(
        string='Instance Name',
        required=True,
        tracking=True,
        help='Name of the SaaS instance'
    )
    
    # Client and Plan
    client_id = fields.Many2one(
        'saas.client',
        string='Client',
        required=True,
        tracking=True,
        ondelete='cascade'
    )
    
    plan_id = fields.Many2one(
        'saas.plan',
        string='Plan',
        required=True,
        tracking=True
    )
    
    # Instance Configuration
    subdomain = fields.Char(
        string='Subdomain',
        required=True,
        help='Subdomain for the instance (e.g., client1)'
    )
    
    domain_id = fields.Many2one(
        'saas.domain',
        string='Domain',
        help='Base domain for the instance'
    )
    
    full_domain = fields.Char(
        string='Full Domain',
        compute='_compute_full_domain',
        store=True,
        help='Complete domain (subdomain + base domain)'
    )
    
    # Instance Type and State
    instance_type = fields.Selection([
        ('trial', 'Trial'),
        ('production', 'Production'),
        ('staging', 'Staging'),
        ('development', 'Development'),
    ], string='Instance Type', default='production', required=True, tracking=True)
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('creating', 'Creating'),
        ('running', 'Running'),
        ('suspended', 'Suspended'),
        ('stopped', 'Stopped'),
        ('error', 'Error'),
        ('terminated', 'Terminated'),
    ], string='Status', default='draft', tracking=True)
    
    # Server and Technical Details
    server_id = fields.Many2one(
        'saas.server',
        string='Server',
        required=True,
        tracking=True
    )
    
    database_name = fields.Char(
        string='Database Name',
        help='Name of the database on the server'
    )
    
    port = fields.Integer(
        string='Port',
        help='Port number for the instance'
    )
    
    container_id = fields.Char(
        string='Container ID',
        help='Docker container ID'
    )
    
    # Odoo Configuration
    odoo_version = fields.Char(
        string='Odoo Version',
        related='plan_id.odoo_version',
        store=True
    )
    
    admin_password = fields.Char(
        string='Admin Password',
        help='Admin password for the instance'
    )
    
    # Resource Usage
    cpu_usage = fields.Float(
        string='CPU Usage (%)',
        help='Current CPU usage percentage'
    )
    
    memory_usage_mb = fields.Integer(
        string='Memory Usage (MB)',
        help='Current memory usage in MB'
    )
    
    storage_usage_gb = fields.Float(
        string='Storage Usage (GB)',
        help='Current storage usage in GB'
    )
    
    # Dates and Timing
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        tracking=True
    )
    
    started_date = fields.Datetime(
        string='Started Date',
        help='Date when instance was started'
    )
    
    last_backup_date = fields.Datetime(
        string='Last Backup',
        help='Date of last backup'
    )
    
    # Trial Management
    trial_end_date = fields.Datetime(
        string='Trial End Date',
        help='End date for trial instances'
    )
    
    is_trial_expired = fields.Boolean(
        string='Trial Expired',
        compute='_compute_trial_status',
        help='Whether trial period has expired'
    )
    
    days_until_trial_end = fields.Integer(
        string='Days Until Trial End',
        compute='_compute_trial_status',
        help='Number of days until trial ends'
    )
    
    # Subscription
    subscription_id = fields.Many2one(
        'saas.subscription',
        string='Subscription',
        help='Related subscription'
    )
    
    # Apps and Modules
    installed_app_ids = fields.Many2many(
        'saas.app',
        'saas_instance_app_rel',
        'instance_id',
        'app_id',
        string='Installed Apps'
    )
    
    # Backups
    backup_ids = fields.One2many(
        'saas.backup',
        'instance_id',
        string='Backups'
    )
    
    backup_count = fields.Integer(
        string='Backup Count',
        compute='_compute_backup_count'
    )
    
    # Custom Domains
    custom_domain_ids = fields.One2many(
        'saas.custom.domain',
        'instance_id',
        string='Custom Domains'
    )
    
    # Access URLs
    instance_url = fields.Char(
        string='Instance URL',
        compute='_compute_instance_url',
        help='URL to access the instance'
    )
    
    # Notes
    notes = fields.Text(
        string='Notes',
        help='Additional notes about the instance'
    )

    @api.depends('subdomain', 'domain_id', 'domain_id.name')
    def _compute_full_domain(self):
        for instance in self:
            if instance.subdomain and instance.domain_id:
                instance.full_domain = f"{instance.subdomain}.{instance.domain_id.name}"
            else:
                instance.full_domain = False

    @api.depends('trial_end_date')
    def _compute_trial_status(self):
        now = fields.Datetime.now()
        for instance in self:
            if instance.trial_end_date:
                instance.is_trial_expired = instance.trial_end_date < now
                delta = instance.trial_end_date - now
                instance.days_until_trial_end = delta.days if delta.days > 0 else 0
            else:
                instance.is_trial_expired = False
                instance.days_until_trial_end = 0

    @api.depends('backup_ids')
    def _compute_backup_count(self):
        for instance in self:
            instance.backup_count = len(instance.backup_ids)

    @api.depends('full_domain', 'port')
    def _compute_instance_url(self):
        for instance in self:
            if instance.full_domain:
                protocol = 'https' if instance.plan_id.ssl_enabled else 'http'
                port_suffix = f":{instance.port}" if instance.port and instance.port != 80 and instance.port != 443 else ""
                instance.instance_url = f"{protocol}://{instance.full_domain}{port_suffix}"
            else:
                instance.instance_url = False

    @api.constrains('subdomain')
    def _check_subdomain_unique(self):
        for instance in self:
            if instance.subdomain and instance.domain_id:
                existing = self.search([
                    ('subdomain', '=', instance.subdomain),
                    ('domain_id', '=', instance.domain_id.id),
                    ('id', '!=', instance.id),
                    ('state', '!=', 'terminated')
                ])
                if existing:
                    raise ValidationError(_('Subdomain must be unique within the same domain.'))

    @api.model
    def create(self, vals):
        # Set trial end date for trial instances
        if vals.get('instance_type') == 'trial' and 'trial_end_date' not in vals:
            plan = self.env['saas.plan'].browse(vals.get('plan_id'))
            if plan and plan.trial_duration_days:
                vals['trial_end_date'] = fields.Datetime.now() + timedelta(days=plan.trial_duration_days)
        
        # Generate database name if not provided
        if 'database_name' not in vals:
            vals['database_name'] = self._generate_database_name(vals.get('subdomain', ''))
        
        # Generate admin password if not provided
        if 'admin_password' not in vals:
            vals['admin_password'] = self._generate_password()
        
        instance = super().create(vals)
        
        # Auto-create instance if configured
        if instance.state == 'draft':
            instance.action_create_instance()
        
        return instance

    def _generate_database_name(self, subdomain):
        """Generate a unique database name"""
        import re
        # Clean subdomain to be database-safe
        clean_subdomain = re.sub(r'[^a-zA-Z0-9_]', '_', subdomain.lower())
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"saas_{clean_subdomain}_{timestamp}"

    def _generate_password(self):
        """Generate a secure password"""
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(12))

    def action_create_instance(self):
        """Create the actual instance"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Instance can only be created from draft state.'))
        
        self.write({'state': 'creating'})
        
        try:
            # Create database
            self._create_database()
            
            # Deploy instance
            self._deploy_instance()
            
            # Install apps
            self._install_apps()
            
            # Configure SSL if enabled
            if self.plan_id.ssl_enabled:
                self._configure_ssl()
            
            self.write({
                'state': 'running',
                'started_date': fields.Datetime.now()
            })
            
            # Send instance ready email
            self._send_instance_ready_email()
            
        except Exception as e:
            _logger.error(f"Failed to create instance {self.name}: {str(e)}")
            self.write({'state': 'error'})
            raise UserError(_('Failed to create instance: %s') % str(e))

    def _create_database(self):
        """Create the database for the instance"""
        # This would typically involve calling Odoo's database creation API
        # For now, we'll simulate the process
        _logger.info(f"Creating database {self.database_name} for instance {self.name}")
        
        # In a real implementation, this would:
        # 1. Connect to PostgreSQL
        # 2. Create the database
        # 3. Initialize Odoo database structure
        pass

    def _deploy_instance(self):
        """Deploy the instance using Docker or similar"""
        # This would typically involve Docker deployment
        _logger.info(f"Deploying instance {self.name} on server {self.server_id.name}")
        
        # In a real implementation, this would:
        # 1. Create Docker container
        # 2. Configure Odoo
        # 3. Start the service
        # 4. Configure reverse proxy
        pass

    def _install_apps(self):
        """Install required apps on the instance"""
        if self.plan_id.included_app_ids:
            _logger.info(f"Installing apps for instance {self.name}")
            # In a real implementation, this would install the apps via Odoo API
            self.installed_app_ids = [(6, 0, self.plan_id.included_app_ids.ids)]

    def _configure_ssl(self):
        """Configure SSL certificate for the instance"""
        _logger.info(f"Configuring SSL for instance {self.name}")
        # In a real implementation, this would use Let's Encrypt or similar
        pass

    def _send_instance_ready_email(self):
        """Send email notification when instance is ready"""
        template = self.env.ref('saas_management.email_template_instance_ready', raise_if_not_found=False)
        if template:
            template.send_mail(self.id, force_send=True)

    def action_start(self):
        """Start the instance"""
        self.ensure_one()
        if self.state not in ['stopped', 'suspended']:
            raise UserError(_('Instance can only be started from stopped or suspended state.'))
        
        # Start the instance
        self._start_instance()
        self.write({
            'state': 'running',
            'started_date': fields.Datetime.now()
        })

    def action_stop(self):
        """Stop the instance"""
        self.ensure_one()
        if self.state != 'running':
            raise UserError(_('Instance can only be stopped from running state.'))
        
        # Stop the instance
        self._stop_instance()
        self.write({'state': 'stopped'})

    def action_suspend(self):
        """Suspend the instance"""
        self.ensure_one()
        if self.state != 'running':
            raise UserError(_('Instance can only be suspended from running state.'))
        
        # Suspend the instance
        self._suspend_instance()
        self.write({'state': 'suspended'})

    def action_terminate(self):
        """Terminate the instance permanently"""
        self.ensure_one()
        
        # Create final backup before termination
        self.action_create_backup()
        
        # Terminate the instance
        self._terminate_instance()
        self.write({'state': 'terminated'})

    def _start_instance(self):
        """Start the instance service"""
        _logger.info(f"Starting instance {self.name}")
        # Implementation would start Docker container or service

    def _stop_instance(self):
        """Stop the instance service"""
        _logger.info(f"Stopping instance {self.name}")
        # Implementation would stop Docker container or service

    def _suspend_instance(self):
        """Suspend the instance service"""
        _logger.info(f"Suspending instance {self.name}")
        # Implementation would pause Docker container or service

    def _terminate_instance(self):
        """Terminate the instance permanently"""
        _logger.info(f"Terminating instance {self.name}")
        # Implementation would remove Docker container and clean up resources

    def action_create_backup(self):
        """Create a backup of the instance"""
        self.ensure_one()
        
        backup_vals = {
            'name': f"Backup {self.name} - {fields.Datetime.now().strftime('%Y-%m-%d %H:%M')}",
            'instance_id': self.id,
            'backup_type': 'manual',
            'state': 'creating',
        }
        
        backup = self.env['saas.backup'].create(backup_vals)
        
        # Start backup process
        backup.action_create_backup()
        
        self.last_backup_date = fields.Datetime.now()
        
        return backup

    def action_view_backups(self):
        """View backups for this instance"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Backups'),
            'res_model': 'saas.backup',
            'view_mode': 'list,form',
            'domain': [('instance_id', '=', self.id)],
            'context': {'default_instance_id': self.id},
        }
